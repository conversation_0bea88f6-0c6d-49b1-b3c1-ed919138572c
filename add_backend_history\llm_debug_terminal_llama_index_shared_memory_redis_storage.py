# =============================
# LLM调试终端 - 显示发送给LLM的完整信息
# =============================

import os
import llama_index.core
from llama_index.core import Settings, VectorStoreIndex, Document
from llama_index.llms.openai import OpenAI
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.core.memory import ChatSummaryMemoryBuffer
from llama_index.core.chat_engine import SimpleChatEngine
from llama_index.core.prompts import PromptTemplate
from llama_index.vector_stores.qdrant import QdrantVectorStore
from qdrant_client import QdrantClient
from llama_index.storage.chat_store.redis import RedisChatStore

# 设置全局处理器显示LLM输入输出
llama_index.core.set_global_handler("simple")

# 配置OpenAI
os.environ["OPENAI_API_KEY"] = "sk-qY0nr8zudg7Wc2bTR8EUV6rOTwfqZlU2ihwGL4pJ6m2ZEkEE"
os.environ["OPENAI_BASE_URL"] = "https://api.openai-proxy.org/v1"

Settings.llm = OpenAI(
    model="gpt-4o-mini", 
    temperature=0,
    api_base="https://api.openai-proxy.org/v1"
)
Settings.embed_model = OpenAIEmbedding(
    model="text-embedding-3-small",
    api_base="https://api.openai-proxy.org/v1"
)

print("✅ 已设置 LLM 和 Embedding。\n")

# 创建测试文档
documents = [
    Document(
        text="Python函数定义使用def关键字，语法为：def function_name(parameters): return value",
        metadata={"course_id": "course_01", "material_id": "material_001"}
    ),
    Document(
        text="函数参数可以是位置参数、关键字参数、默认参数和可变参数(*args, **kwargs)",
        metadata={"course_id": "course_01", "material_id": "material_002"}
    ),
    Document(
        text="Python变量不需要声明类型，支持动态类型。常见类型包括int、float、str、list、dict等",
        metadata={"course_id": "course_01", "material_id": "material_003"}
    )
]

# 创建向量索引
client = QdrantClient(location=":memory:")
vector_store = QdrantVectorStore(client=client, collection_name="test_docs")
index = VectorStoreIndex.from_documents(documents, vector_store=vector_store)

print("✅ 知识库已建立\n")

# 创建共享内存
chat_store = RedisChatStore(redis_url="redis://localhost:6379", ttl=3600)

custom_summary_prompt = """你是对话记忆助理。请在 300 字内总结用户问的主要问题，困惑点，以及已经给出的关键信息、结论和思路。
"""

memory = ChatSummaryMemoryBuffer.from_defaults(
    token_limit=1000,
    llm=Settings.llm,
    chat_store=chat_store,
    chat_store_key="debug_chat",
    summarize_prompt=custom_summary_prompt
)

print("✅ 共享内存已创建\n")

# 创建condense_plus_context引擎
new_condense_prompt = PromptTemplate(
    "你是一个RAG（检索增强生成）专家，你将根据用户和AI助手之前的聊天历史，把学生最新提出的问题，改写成一个详细完整具体的、携带必要上下文的问题，可以是陈述句也可以疑问句。\n"
    "注意，你改写后的问题将会用于通过向量检索来获取与问题最相关的文本块。\n"
    "=== 聊天历史 ===\n"
    "{chat_history}\n\n"
    "=== 学生最新提出的问题 ===\n"
    "{question}\n\n"
    "=== 改写后的独立问题 ===\n"
)

custom_context_prompt = (
    "你叫做文文，一个专业的热心活泼乐于助人的ai聊天助手，擅长查找学习资料，而且你总是喜欢用理查德·费曼的风格讲解学习资料。你总是用排版清晰的markdown格式回答问题，用很多的emoji让内容更生动。\n\n"
    "📚 **相关文档内容：**\n"
    "{context_str}\n\n"
    "🎯 **回答要求：**\n"
    "1. 严格基于上述文档内容进行回答\n"
    "2. 如果文档内容不足以回答问题，请明确说明'文档中暂无相关信息'\n"
    "3. 回答要条理清晰，使用适当的emoji让内容更生动\n"
    "4. 请引用具体的文档内容来支撑你的回答\n\n"
    "💡 **请基于以上文档和之前的对话历史来回答用户的问题。**"
     "根据以上信息，请回答这个问题: {query_str}\n\n" #这里放一个{query_str}可以但是可能不合适，不过我觉得是最佳实践 ---by James
     "====================接下来都是历史聊天记录，你关键要找到用户最后问的问题认真回答========================\n\n"
)

# 在condense_question_plus模式下，给llm的内容是context_prompt_template + system_prompt + chat_history + query_str,所以其实system_prompt没有必要


condense_question_plus_engine = index.as_chat_engine(
    chat_mode="condense_plus_context",
    condense_prompt=new_condense_prompt,
    context_prompt=custom_context_prompt,
    memory=memory,
    # system_prompt="你叫做文文，一个专业的热心活泼乐于助人的ai聊天助手，擅长查找学习资料，而且你总是喜欢用理查德·费曼的风格讲解学习资料。你总是用排版清晰的markdown格式回答问题，用很多的emoji让内容更生动。",
    verbose=True,
)

# 创建simple引擎
simple_engine = SimpleChatEngine.from_defaults(
    llm=Settings.llm,
    memory=memory,
    system_prompt="你叫做文文，一个专业的热心活泼乐于助人的ai聊天助手，擅长查找学习资料，而且你总是喜欢用理查德·费曼的风格讲解学习资料。你总是用排版清晰的markdown格式回答问题，用很多的emoji让内容更生动。",
    verbose=True
)

print("✅ 两个引擎均已就绪\n")

# 交互式对话终端
print("\n" + "="*90)
print("🎯 欢迎使用LLM调试终端！")
print("📋 可用命令：")
print("   1 - 切换到 condense_plus_context 模式（RAG检索）")
print("   2 - 切换到 simple 模式（纯对话）")
print("   /memory - 查看当前记忆状态")
print("   /reset - 重置对话记忆")
print("   /quit - 退出")
print("="*90 + "\n")

current_engine = condense_question_plus_engine
current_mode = "condense_plus_context"

while True:
    mode_display = "🔎 RAG检索" if current_mode == "condense_plus_context" else "💬 纯对话"
    user_input = input(f"[{mode_display}] 👤 你: ").strip()
    
    if not user_input:
        continue
        
    # 处理命令
    if user_input == "1":
        current_engine = condense_question_plus_engine
        current_mode = "condense_plus_context"
        print("✅ 已切换到 condense_plus_context 模式（RAG检索）\n")
        continue
    elif user_input == "2":
        current_engine = simple_engine
        current_mode = "simple"
        print("✅ 已切换到 simple 模式（纯对话）\n")
        continue
    elif user_input == "/memory":
        print("📝 当前记忆状态:")
        messages = memory.get()
        for i, msg in enumerate(messages):
            role = msg.role.value if hasattr(msg.role, 'value') else str(msg.role)
            content = msg.content[:200] + "..." if len(msg.content) > 200 else msg.content
            print(f"[{i}] {role.upper()}: {content}")
        print()
        continue
    elif user_input == "/reset":
        memory.reset()
        print("🔄 记忆已重置\n")
        continue
    elif user_input == "/quit":
        print("👋 再见！")
        break
        
    # 处理用户问题
    print(f"\n🤖 {current_mode} 回答：")
    print("="*80)
    print("📤 发送给LLM的信息将在下方显示:")
    print("="*80)
    
    response = current_engine.chat(user_input)
    
    print("="*80)
    print(f"📥 LLM回答: {response}")
    print("="*80 + "\n")
