
# 🧠 LlamaIndex Memory Blocks 与 **FactExtractionMemoryBlock** 详解（费曼风格）

> 把它想成一套“**对话记忆管理系统**”：**短期历史**像白板，写满就擦最旧的一段；被擦掉的内容会被“长期记忆模块（Memory Blocks）”**接住、提炼或存档**。目标是在有限的 token 预算里，既记住关键事，又让回答稳、准、便宜。

---

## 🧭 TL;DR（先给结论）
- 三类 **Memory Blocks**：
  - **🧷 StaticMemoryBlock**：放**静态背景**（如用户档案/系统设定），几乎总保留。
  - **🧪 FactExtractionMemoryBlock**：把“被冲刷的旧消息”**榨成客观事实列表**，供后续回答用。
  - **🗂️ VectorMemoryBlock**：把旧消息丢进**向量库**，需要时按语义检索片段回填。
- **抽取事实**并非每轮都跑：当短期历史超配额时，按 **`token_flush_size`** 把最旧的一批消息“冲刷”给各个 Block；**事实块**这时才会对这批消息做抽取。
- 事实块有**容量上限**：**`max_facts`**。超过就会**压缩/合并**（condense）。
- 最终回答时：会把**保留下的短期历史** + **各 Block 渲染后的文本**（位置由 `insert_method` 决定，保留顺序受 `priority` 影响）一起喂给 LLM，再受 **`token_limit`** 做最后截断。

---

## 🧩 三种 Memory Blocks（速览）

### 1) 🧷 StaticMemoryBlock（静态块）
- **用来放：** 不变的背景资料、系统角色设定、用户固定档案（如公司名、职位）。
- **特点：** 通常优先级高，**总是被带入**上下文（除非你显式降低优先级或 token 极度紧张）。
- **作用：** 给模型稳定的“世界常识/边界条件”。

### 2) 🧪 FactExtractionMemoryBlock（事实块）
- **用来放：** 从旧消息中**提取出来的“客观、可验证的原子事实”**（如“用户在上海工作”“项目预算 50 万”等）。
- **特点：** 控制条数：`max_facts`；可自定义**抽取/压缩**提示词；支持 `priority` 与 `insert_method`。
- **作用：** 让系统在对话推进中，**持续积累关键结论**，并以**结构化**（如 XML/要点式）方式回灌给模型。

### 3) 🗂️ VectorMemoryBlock（向量块）
- **用来放：** 被冲刷的原始片段（embedding 入库），在需要时**按相似度检索**若干条回填。
- **特点：** 用 `similarity_top_k` 控制检索条数；设为 **0** 即“**只存不取**”。
- **作用：** 当事实块不适合“压缩为事实”，但未来**可能需要原句上下文**时很有用。

---

## 🧠 统一的内存预算心智模型
- **`token_limit`（总额）**：最终传给 LLM 的上下文上限（**短期历史 + 各 Block 渲染文本**）。
- **`chat_history_token_ratio`（历史配额比例）**：至少留给**短期历史**的占比，超过后就会触发**冲刷**。
- **`token_flush_size`（冲刷批量）**：每次把**最旧的一段**历史（按 token 计）打包交给各 Block 处理。

> 你可以把它想成：**先保历史，再把溢出的旧消息“外包”给 Blocks（事实提炼、向量存档等）**，最后统一拼接回上下文。

---

# 🔬 FactExtractionMemoryBlock —— 从“旧消息”榨出“可复用事实”

## 1) “什么才算一个 fact”？
- **客观、可验证、可独立引用**的**原子陈述**（避免主观评价/推测）。  
- **来自对话本身**（不引入模型先验）。  
- 典型示例：  
  - ✅ “用户在上海工作。”  
  - ✅ “公司名为智算科技，成立于 2018 年。”  
  - ❌ “公司很有潜力。”（主观）  
  - ❌ “用户可能在金融行业。”（猜测）  
- 默认抽取格式常用**结构化**（如 XML）：
  ```xml
  <facts>
    <fact>用户在上海工作</fact>
    <fact>公司：智算科技</fact>
    <fact>成立年份：2018</fact>
  </facts>
  ```

## 2) 事实**抽取**是何时进行的？
- **并不是每轮都抽取**。  
- 当短期历史占用超过 `chat_history_token_ratio * token_limit`，系统会把**最旧的一批**消息，按 **`token_flush_size`** 的体量**冲刷**出去；**事实块**这时收到这批消息并进行**抽取**。

### 抽取时，LLM 收到什么？
- **输入 =** 这批被冲刷的**旧消息**（文本） **+** 目前已有的 **`existing_facts`**（用于去重/合并）。  
- **提示词模板**：`fact_extraction_prompt_template`（可自定义为中文、领域术语、要点数限制等）。

### 抽取结果存到哪里？
- 存入事实块内部的 facts 列表：**`existing_facts`**。

## 3) 事实**容量与限制**
- **`max_facts`**：事实数量上限（例如 50）。  
- 当新增事实导致 **`existing_facts` > max_facts`** 时，触发**压缩/合并**流程（见下节）。

## 4) 事实**压缩/合并（condense）**如何进行？
- **触发条件**：新增后**超过 `max_facts`**。  
- **压缩输入**：当前的 **`existing_facts`**（而非整段对话历史）。  
- **提示词模板**：`fact_condense_prompt_template`（可自定义**合并策略与输出上限**，如“优先保留可操作信息/具体数字/时间/实体”）。  
- **目标**：去重、合并近义/重复项，**把事实清单缩回不超过 `max_facts`**。

## 5) 事实如何被**注入**到最终回答？
- **插入位置**由 `insert_method` 决定：
  - `"system"`：把 facts 当作**系统前提**，放在最前面；常用于“用户档案/设定”。
  - `"last_user"`：把 facts 插到**最后一条用户消息前**，让它们像“当前问题的上下文补充”。
- **保留顺序**由 `priority` 影响：
  - **数字越小越优先**；在总 token 超限时，高优先级块更不容易被截断。  
  - 实战里把事实块设为 **`priority=0`** 可**尽量**确保它总被带入（最终仍需服从总 `token_limit` 的硬上限）。
- **最终拼接**：**短期历史 +（静态块/事实块/向量块等渲染文本）+ 当前用户问题** → 再按 `token_limit` 截断。

## 6) 调参与实践建议（📐实用向）
- **事实抽取力度**：用 `fact_extraction_prompt_template` 明确“**什么算事实**、**输出格式**、**最多要点数**”，减少噪声。  
- **历史与长期的比例**：
  - 高频连续追问 → **提高** `chat_history_token_ratio`，多留原文上下文；
  - 资料密集型场景 → **降低**一点比例，让事实/向量有更大舞台。
- **`token_flush_size`**：
  - 太小 → 抽取频繁但每次信息碎片化；
  - 太大 → 单次抽取上下文臃肿、成本高。  
  可从 **模型 1/10~1/5 上下文**做起点试探。
- **只存不取向量**：`VectorMemoryBlock(similarity_top_k=0)`，事实块照常注入；等需要召回再改 >0。  
- **可观测性**：定期打印 `existing_facts`，检查是否“过泛/遗漏/有主观色彩”，迭代抽取与压缩模板。

## 7) 常见误区与边界（🧱）
- **误以为“每轮都抽取事实”**：只有**超过历史配额**、发生“冲刷”时才抽取。  
- **把“摘要”当事实**：事实需要**客观与可验证**；“感觉/结论”请交给别的 Block 或在事实模板里明确排除。  
- **忽略优先级/插入位置**：`priority` 会影响超限截断；`insert_method` 影响事实在语义上的“权重”。  
- **无限信任向量检索**：向量块是“召回”，不等于“事实”；重要信息**尽量进事实块**，确保稳态可见。

---

# 📚 FAQ（结合我们之前的问答）

1. **什么是 fact？**  
   对话中直接给出的、**客观/可验证/原子化**的陈述；排除主观评价、猜测。

2. **何时抽取？每轮都抽吗？**  
   **不**是每轮。只有当**短期历史超配额**被**冲刷**时，按 `token_flush_size` 批量把**最旧消息**交给事实块抽取。

3. **抽取时发给 LLM 的是什么？**  
   **被冲刷的旧消息** + **现有 `existing_facts`**（用于去重），由 `fact_extraction_prompt_template` 组织。

4. **事实的数量有上限吗？怎么设？**  
   有：`max_facts`。超过则触发**压缩**，用 `fact_condense_prompt_template` 合并缩回上限。

5. **最终回答会带上 facts 吗？**  
   会。事实与保留下的历史一起被拼入上下文；**位置**由 `insert_method` 决定，**保留**受 `priority` 与 `token_limit` 影响。

6. **能设置事实块优先级与插入位置吗？**  
   能：`priority`（越小越不易被截断），`insert_method`（`system` / `last_user`）。

7. **向量块能“只存不取”吗？**  
   能：把 `similarity_top_k=0` 即可。

---

## ✅ 最小工作清单（落地用）
- 设定：`token_limit`、`chat_history_token_ratio`、`token_flush_size`  
- 事实块：`max_facts`、`fact_extraction_prompt_template`、`fact_condense_prompt_template`、`priority`、`insert_method`  
- 向量块：`similarity_top_k`（需要时再 >0）  
- 可观测：定期打印 `existing_facts`，按噪声和缺漏迭代模板与阈值。

---

**用一句话收尾**：  
> 把 FactExtractionMemoryBlock 当作你的“对话知识蒸馏器”。当历史溢出时，它只收可验证的“硬事实”，再通过优先级与插入位置，把这些关键点稳稳地送进每一轮回答的“心脏地带”。
