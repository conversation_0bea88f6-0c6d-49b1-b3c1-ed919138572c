{"name": "@bokeh/jupyter_bokeh", "version": "4.0.5", "description": "A Jupyter extension for rendering Bokeh content.", "keywords": ["jup<PERSON><PERSON>", "jupyterlab", "jupyterlab-extension"], "homepage": "https://github.com/bokeh/jupyter_bokeh", "bugs": {"url": "https://github.com/bokeh/jupyter_bokeh/issues"}, "license": "BSD-3-<PERSON><PERSON>", "author": {"name": "Bokeh team", "email": "<EMAIL>"}, "files": ["{dist,lib}/**/*.{d.ts,eot,gif,html,jpg,js,js.map,json,png,svg,woff2,ttf}", "style/**/*.{css,.js,eot,gif,html,jpg,json,png,svg,woff2,ttf}"], "main": "lib/index.js", "types": "lib/index.d.ts", "style": "style/index.css", "repository": {"type": "git", "url": "https://github.com/bokeh/jupyter_bokeh.git"}, "scripts": {"build": "jlpm run build:lib && jlpm run build:nbextension && jlpm run build:labextension:dev", "build:prod": "jlpm run build:lib && jlpm run build:nbextension && jlpm run build:labextension", "build:labextension": "jupyter labextension build .", "build:labextension:dev": "jupyter labextension build --development True .", "build:nbextension": "webpack --mode=production", "build:lib": "tsc", "clean": "jlpm run clean:lib", "clean:lib": "rimraf lib tsconfig.tsbuildinfo", "clean:labextension": "rimraf jupyter_bokeh/labextension", "clean:all": "jlpm run clean:lib && jlpm run clean:labextension", "eslint": "eslint . --ext .ts,.tsx --fix", "eslint:check": "eslint . --ext .ts,.tsx", "install:extension": "jupyter labextension develop --overwrite .", "__prepare": "jlpm run clean && jlpm run build:prod", "watch": "run-p watch:src watch:labextension", "watch:src": "tsc -w", "watch:labextension": "jupyter labextension watch ."}, "dependencies": {"@jupyter-widgets/base": "^2 || ^3 || ^4 || ^5 || ^6", "@jupyterlab/application": "^4", "@jupyterlab/apputils": "^4", "@jupyterlab/docregistry": "^4", "@jupyterlab/notebook": "^4", "@jupyterlab/services": "^7", "@jupyterlab/settingregistry": "^4", "@jupyterlab/ui-components": "^4", "@lumino/disposable": "^2", "css-loader": "^5.1.3", "style-loader": "^2.0.0"}, "resolutions": {"@lumino/widgets": "^2", "react": "^18.2.0", "react-dom": "^18.2.0"}, "peerDependencies": {"@jupyter-widgets/jupyterlab-manager": "^5.0.4"}, "devDependencies": {"@jupyterlab/builder": "^4", "@typescript-eslint/eslint-plugin": "^7.0.1", "@typescript-eslint/parser": "^7.0.1", "eslint": "^8.36.0", "npm-run-all": "^4.1.5", "rimraf": "^4.4.1", "source-map-loader": "^5.0.0", "typescript": "~5.0.2", "webpack": "^5.75.0", "webpack-cli": "^4.10.0"}, "sideEffects": ["style/*.css", "style/index.js"], "styleModule": "style/index.js", "jupyterlab": {"extension": true, "outputDir": "jupyter_bokeh/labextension", "_build": {"load": "static/remoteEntry.46e48fc13a737187eb1c.js", "extension": "./extension", "style": "./style"}}}