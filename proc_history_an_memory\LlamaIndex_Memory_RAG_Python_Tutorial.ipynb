{"cells": [{"cell_type": "markdown", "id": "0e05a69d", "metadata": {}, "source": ["\n", "# 🐍📚 LlamaIndex Memory + RAG（Python 教程场景）一步到位 Notebook\n", "> 讲解人：**“费曼风格”的编程教育家**（就是我 😄）  \n", "> 目标：**最少魔法、可跑、可观察**——你能清楚看到每一步到底做了什么。\n", "\n", "---\n", "\n", "## ✅ 本 Notebook 你将学到\n", "1. **同时创建两种 chat_engine**：\n", "   - `chat_mode=\"condense_question\"`（RAG 问答，先独立化问题再检索）\n", "   - `chat_mode=\"simple\"`（不检索，纯模型对话）\n", "   - **两者共享 *同一个* 新版 `Memory`**，其中包含：`StaticMemoryBlock` 与 `FactExtractionMemoryBlock`（**不使用 VectorMemoryBlock**）。\n", "\n", "2. **读取 + 打印 + 自定义** `FactExtractionMemoryBlock` 的两段提示词：  \n", "   - `fact_extraction_prompt_template`（抽取事实）  \n", "   - `fact_condense_prompt_template`（压缩事实）\n", "\n", "3. **用 SentenceSplitter 进行文本分块**，构建一个**针对“Python 教程”**的最小可跑 RAG；多轮问答时让两个 chat_engine **共享同一份记忆**。\n", "\n", "4. **加餐**：演示**两个 chat_engine 共享同一个 `ChatSummaryMemoryBuffer`** 进行多轮对话（便于观察“总结式”短期记忆）。\n", "\n", "---\n", "\n", "## 📦 环境&运行说明\n", "- **零自定义函数、零异常处理**：所有代码都尽量“直给”。\n", "- 你需要准备一个 `OPENAI_API_KEY`（或自行替换为你偏好的 LLM/Embedding）。\n", "- **所有 print 输出**会使用长分隔符、emoji 和换行，方便你**肉眼观察**每步的中间数据与规律。\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "6ba0c8a9", "metadata": {}, "outputs": [], "source": ["\n", "# === 安装依赖（如你本地已有，可跳过） ======================================\n", "%pip -q install --upgrade llama-index-core llama-index-llms-openai llama-index-embeddings-openai tiktoken\n"]}, {"cell_type": "code", "execution_count": 1, "id": "d1dd3190", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 已设置 LLM 和 Embedding。\n", "\n"]}], "source": ["# =============================\n", "# 1) 基础导入与全局设置\n", "# =============================\n", "import os\n", "from llama_index.core import Settings, VectorStoreIndex, PromptTemplate\n", "from llama_index.core import SimpleDirectoryReader\n", "from llama_index.core.node_parser import SentenceSplitter\n", "from llama_index.llms.openai import OpenAI\n", "from llama_index.embeddings.openai import OpenAIEmbedding\n", "from llama_index.core.memory import ChatSummaryMemoryBuffer\n", "from llama_index.core.chat_engine import SimpleChatEngine\n", "\n", "# 替换成你自己的 OpenAI API Key\n", "os.environ[\"OPENAI_API_KEY\"] = \"sk-qY0nr8zudg7Wc2bTR8EUV6rOTwfqZlU2ihwGL4pJ6m2ZEkEE\"\n", "# 如需自定义网关（如代理或 Azure），取消下面注释并替换\n", "os.environ[\"OPENAI_BASE_URL\"] = \"https://api.openai-proxy.org/v1\"\n", "\n", "\n", "\n", "# ➊ 配置全局设置（替代 ServiceContext）\n", "Settings.llm = OpenAI(\n", "    model=\"gpt-4o-mini\", \n", "    temperature=0,\n", "    api_base=\"https://api.openai-proxy.org/v1\"\n", ")\n", "Settings.embed_model = OpenAIEmbedding(\n", "    model=\"text-embedding-3-small\",\n", "    api_base=\"https://api.openai-proxy.org/v1\"\n", ")\n", "\n", "\n", "print(\"✅ 已设置 LLM 和 Embedding。\\n\")"]}, {"cell_type": "code", "execution_count": 2, "id": "dbcd3458", "metadata": {}, "outputs": [{"data": {"text/plain": ["Document(id_='f3cc9c84-4485-4b4c-928c-21a1ddd69604', embedding=None, metadata={'file_path': 'c:\\\\Users\\\\<USER>\\\\Desktop\\\\ai_mindmap\\\\proc_history_an_memory\\\\..\\\\llama_data\\\\python第八章.md', 'file_name': 'python第八章.md', 'file_size': 48172, 'creation_date': '2025-08-07', 'last_modified_date': '2025-07-16', 'course_id': 'course_01', 'course_material_id': 'material_001'}, excluded_embed_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, metadata_template='{key}: {value}', metadata_separator='\\n', text_resource=MediaResource(embeddings=None, data=None, text='# Python 第八章 函数\\n\\n在本章中，你将学习编写**函数**（function）。函数是带名字的代码块，用于完成具体的工作。要执行函数定义的特定任务，可**调用**（call）该函数。当需要在程序中多次执行同一项任务时，无须反复编写完成该任务的代码，只需要调用执行该任务的函数，让 Python 运行其中的代码即可。你将发现，使用函数，程序编写、阅读、测试和修复起来都会更容易。\\n\\n你还将学习各种向函数传递信息的方式，学习编写主要任务是显示信息的函数，以及用于处理数据并返回一个或一组值的函数。最后，你将学习如何将函数存储在称为**模块**（module）的独立文件中，让主程序文件更加整洁。\\n\\n## 定义函数\\n\\n下面是一个打印问候语的简单函数，名为 greet_user()：\\n\\n### greeter.py 示例\\n\\n```python\\ndef greet_user():\\n    \"\"\" 显示简单的问候语 \"\"\"\\n    print(\"Hello!\")\\n\\ngreet_user()\\n```\\n\\n#### 函数定义的基本结构\\n\\n这个示例演示了最简单的函数结构。第一行代码使用关键字 def 来告诉 Python，你要定义一个函数。这是**函数定义**，向 Python 指出了函数名，还可以在括号内指出函数为完成任务需要什么样的信息。在这里，函数名为 greet_user()，它不需要任何信息就能完成工作，因此括号内是空的（即便如此，括号也必不可少）。最后，定义以冒号结尾。\\n\\n#### 函数体和文档字符串\\n\\n紧跟在 def greet_user():后面的所有缩进行构成了函数体。第二行的文本是称为**文档字符串**（docstring）的注释，描述了函数是做什么的。Python 在为程序中的函数生成文档时，会查找紧跟在函数定义后的字符串。这些字符串通常前后分别用三个双引号引起，能够包含多行。\\n\\n代码行 print(\"Hello!\")是函数体内的唯一行代码，因此 greet_user()只做一项工作：打印 Hello!。\\n\\n#### 函数调用\\n\\n要使用这个函数，必须调用它。**函数调用**让 Python 执行函数中的代码。要调用函数，可依次指定函数名以及用括号括起的必要信息。由于这个函数不需要任何信息，调用它时只需输入 greet_user()即可。和预期的一样，它会打印 Hello!：\\n\\n```\\nHello!\\n```\\n\\n### 向函数传递信息\\n\\n只需稍作修改，就可让 greet_user()函数在问候用户时以其名字作为抬头。为此，可在函数定义 def greet_user()的括号内添加 username。这样，可让函数接受你给 username 指定的任何值。现在，这个函数要求你在调用它时给 username 指定一个值。因此在调用 greet_user()时，可将一个名字传递给它，如下所示：\\n\\n```python\\ndef greet_user(username):\\n    \"\"\" 显示简单的问候语 \"\"\"\\n    print(f\"Hello, {username.title()}!\")\\n\\ngreet_user(\\'jesse\\')\\n```\\n\\n#### 函数调用的结果\\n\\n代码 greet_user(\\'jesse\\')调用函数 greet_user()，并向它提供执行函数调用 print()所需的信息。这个函数接受你传递给它的名字，并向这个人发出问候：\\n\\n```\\nHello, Jesse!\\n```\\n\\n同样，greet_user(\\'sarah\\')调用函数 greet_user()并向它传递\\'sarah\\'，从而打印 Hello, Sarah!。你可以根据需要调用函数 greet_user()任意多次，无论在调用时传入什么名字，都将生成相应的输出。\\n\\n### 实参和形参\\n\\n前面在定义 greet_user()函数时，要求给变量 username 指定一个值。这样，在调用这个函数并提供这种信息（人名）时，它将打印相应的问候语。\\n\\n#### 形参的概念\\n\\n在 greet_user()函数的定义中，变量 username 是一个**形参**（parameter），即函数完成工作所需的信息。\\n\\n#### 实参的概念\\n\\n在代码 greet_user(\\'jesse\\')中，值\\'jesse\\'是一个**实参**（argument），即在调用函数时传递给函数的信息。在调用函数时，我们将要让函数使用的信息放在括号内。在 greet_user(\\'jesse\\')这个示例中，我们将实参\\'jesse\\'传递给函数 greet_user()，这个值被赋给了形参 username。\\n\\n注意：大家有时候会形参、实参不分。即使你看到有人将函数定义中的变量称为实参或将函数调用中的变量称为形参，也不要大惊小怪。\\n\\n## 传递实参\\n\\n函数定义中可能包含多个形参，因此函数调用中也可能包含多个实参。向函数传递实参的方式很多：既可以使用**位置实参**，这要求实参的顺序与形参的顺序相同；也可以使用**关键字实参**，其中每个实参都由变量名和值组成；还可以使用列表和字典。下面依次介绍这些方式。\\n\\n### 位置实参\\n\\n在调用函数时，Python 必须将函数调用中的每个实参关联到函数定义中的一个形参。最简单的方式是基于实参的顺序进行关联。以这种方式关联的实参称为**位置实参**。\\n\\n为了明白其中的工作原理，我们来看一个显示宠物信息的函数。这个函数指出一个宠物属于哪种动物以及它叫什么名字，如下所示：\\n\\n#### pets.py 示例\\n\\n```python\\ndef describe_pet(animal_type, pet_name):\\n    \"\"\" 显示宠物的信息 \"\"\"\\n    print(f\"\\\\nI have a {animal_type}.\")\\n    print(f\"My {animal_type}\\'s name is {pet_name.title()}.\")\\n\\ndescribe_pet(\\'hamster\\', \\'harry\\')\\n```\\n\\n这个函数的定义表明，它需要一个动物类型和一个名字。在调用 describe_pet()时，需要按顺序提供一个动物类型和一个名字。例如，在刚才的函数调用中，实参\\'hamster\\'被赋给形参 animal_type，而实参\\'harry\\'被赋给形参 pet_name。在函数体内，使用这两个形参来显示宠物的信息。\\n\\n输出描述了一只名为 Harry 的仓鼠：\\n\\n```\\nI have a hamster.\\nMy hamster\\'s name is Harry.\\n```\\n\\n#### 调用函数多次\\n\\n可根据需要调用函数任意多次。要再描述一个宠物，只需再次调用 describe_pet()即可：\\n\\n```python\\ndef describe_pet(animal_type, pet_name):\\n    \"\"\" 显示宠物的信息 \"\"\"\\n    print(f\"\\\\nI have a {animal_type}.\")\\n    print(f\"My {animal_type}\\'s name is {pet_name.title()}.\")\\n\\ndescribe_pet(\\'hamster\\', \\'harry\\')\\ndescribe_pet(\\'dog\\', \\'willie\\')\\n```\\n\\n第二次调用 describe_pet()函数时，向它传递实参\\'dog\\'和\\'willie\\'。与第一次调用时一样，Python 将实参\\'dog\\'关联到形参 animal_type，并将实参\\'willie\\'关联到形参 pet_name。\\n\\n与前面一样，这个函数完成了任务，但打印的是一条名为 Willie 的小狗的信息。至此，有一只名为 Harry 的仓鼠，还有一条名为 Willie 的小狗：\\n\\n```\\nI have a hamster.\\nMy hamster\\'s name is Harry.\\n\\nI have a dog.\\nMy dog\\'s name is Willie.\\n```\\n\\n多次调用同一个函数是一种效率极高的工作方式。只需在函数中编写一次描述宠物的代码，每当需要描述新宠物时，就都可以调用这个函数并向它提供新宠物的信息。即便描述宠物的代码增加到了 10 行，依然只需使用一行调用函数的代码，就可以描述一个新宠物。\\n\\n在函数中，可根据需要使用任意数量的位置实参，Python 将按顺序将函数调用中的实参关联到函数定义中相应的形参。\\n\\n#### 位置实参的顺序很重要\\n\\n当使用位置实参来调用函数时，如果实参的顺序不正确，结果可能会出乎意料：\\n\\n```python\\ndef describe_pet(animal_type, pet_name):\\n    \"\"\" 显示宠物的信息 \"\"\"\\n    print(f\"\\\\nI have a {animal_type}.\")\\n    print(f\"My {animal_type}\\'s name is {pet_name.title()}.\")\\n\\ndescribe_pet(\\'harry\\', \\'hamster\\')\\n```\\n\\n在这个函数调用中，先指定名字，再指定动物类型。由于实参\\'harry\\'在前，这个值将被赋给形参 animal_type，而后面的\\'hamster\\'将被赋给形参 pet_name。结果是有一个名为 Hamster 的 harry：\\n\\n```\\nI have a harry.\\nMy harry\\'s name is Hamster.\\n```\\n\\n如果你得到的结果像上面一样可笑，请确认函数调用中实参的顺序与函数定义中形参的顺序是否一致。\\n\\n### 关键字实参\\n\\n**关键字实参**是传递给函数的名值对。这样会直接在实参中将名称和值关联起来，因此向函数传递实参时就不会混淆了（不会得到名为 Hamster 的 harry 这样的结果）。关键字实参不仅让你无须考虑函数调用中的实参顺序，而且清楚地指出了函数调用中各个值的用途。\\n\\n下面重新编写 pets.py，在其中使用关键字实参来调用 describe_pet()：\\n\\n```python\\ndef describe_pet(animal_type, pet_name):\\n    \"\"\" 显示宠物的信息 \"\"\"\\n    print(f\"\\\\nI have a {animal_type}.\")\\n    print(f\"My {animal_type}\\'s name is {pet_name.title()}.\")\\n\\ndescribe_pet(animal_type=\\'hamster\\', pet_name=\\'harry\\')\\n```\\n\\n#### 关键字实参的优势\\n\\ndescribe_pet()函数还和之前一样，但这次调用这个函数时，向 Python 明确地指出了各个实参对应的形参。当看到这个函数调用时，Python 知道应该将实参\\'hamster\\'和\\'harry\\'分别赋给形参 animal_type 和 pet_name。输出正确无误，指出有一只名为 Harry 的仓鼠。\\n\\n关键字实参的顺序无关紧要，因为 Python 知道各个值该被赋给哪个形参。下面两个函数调用是等效的：\\n\\n```python\\ndescribe_pet(animal_type=\\'hamster\\', pet_name=\\'harry\\')\\ndescribe_pet(pet_name=\\'harry\\', animal_type=\\'hamster\\')\\n```\\n\\n注意：在使用关键字实参时，务必准确地指定函数定义中的形参名。\\n\\n### 默认值\\n\\n在编写函数时，可以给每个形参指定**默认值**。如果在调用函数中给形参提供了实参，Python 将使用指定的实参值；否则，将使用形参的默认值。因此，给形参指定默认值后，可在函数调用中省略相应的实参。使用默认值不仅能简化函数调用，还能清楚地指出函数的典型用法。\\n\\n如果你发现在调用 describe_pet()时，描述的大多是小狗，就可将形参 animal_type 的默认值设置为\\'dog\\'。这样，当调用 describe_pet()来描述小狗时，就可以不提供该信息：\\n\\n```python\\ndef describe_pet(pet_name, animal_type=\\'dog\\'):\\n    \"\"\" 显示宠物的信息 \"\"\"\\n    print(f\"\\\\nI have a {animal_type}.\")\\n    print(f\"My {animal_type}\\'s name is {pet_name.title()}.\")\\n\\ndescribe_pet(pet_name=\\'willie\\')\\n```\\n\\n#### 默认值的使用规则\\n\\n这里修改了 describe_pet()函数的定义，在其中给形参 animal_type 指定了默认值\\'dog\\'。这样，在调用这个函数时，如果没有给 animal_type 指定值，Python 将自动把这个形参设置为\\'dog\\'：\\n\\n```\\nI have a dog.\\nMy dog\\'s name is Willie.\\n```\\n\\n请注意，在这个函数的定义中，修改了形参的排列顺序。由于给 animal_type 指定了默认值，无须通过实参来指定动物类型，因此函数调用只包含一个实参——宠物的名字。然而，Python 依然将这个实参视为位置实参，如果函数调用只包含宠物的名字，这个实参将被关联到函数定义中的第一个形参。这就是需要将 pet_name 放在形参列表开头的原因。\\n\\n现在，使用这个函数的最简单方式是，在函数调用中只提供小狗的名字：\\n\\n```python\\ndescribe_pet(\\'willie\\')\\n```\\n\\n这个函数调用的输出与前一个示例相同。只提供了一个实参\\'willie\\'，这个实参将被关联到函数定义中的第一个形参 pet_name。由于没有给 animal_type 提供实参，因此 Python 使用默认值\\'dog\\'。\\n\\n如果要描述的动物不是小狗，可使用类似于下面的函数调用：\\n\\n```python\\ndescribe_pet(pet_name=\\'harry\\', animal_type=\\'hamster\\')\\n```\\n\\n由于显式地给 animal_type 提供了实参，Python 将忽略这个形参的默认值。\\n\\n注意：当使用默认值时，必须在形参列表中先列出没有默认值的形参，再列出有默认值的形参。这让 Python 依然能够正确地解读位置实参。\\n\\n### 等效的函数调用\\n\\n鉴于可混合使用位置实参、关键字实参和默认值，通常有多种等效的函数调用方式。请看 describe_pet()函数的如下定义，其中给一个形参提供了默认值：\\n\\n```python\\ndef describe_pet(pet_name, animal_type=\\'dog\\'):\\n```\\n\\n基于这种定义，在任何情况下都必须给 pet_name 提供实参。在指定该实参时，既可以使用位置实参，也可以使用关键字实参。如果要描述的动物不是小狗，还必须在函数调用中给 animal_type 提供实参。同样，在指定该实参时，既可以使用位置实参，也可以使用关键字实参。\\n\\n下面对这个函数的所有调用都可行：\\n\\n```python\\n# 一条名为Willie的小狗\\ndescribe_pet(\\'willie\\')\\ndescribe_pet(pet_name=\\'willie\\')\\n\\n# 一只名为Harry的仓鼠\\ndescribe_pet(\\'harry\\', \\'hamster\\')\\ndescribe_pet(pet_name=\\'harry\\', animal_type=\\'hamster\\')\\ndescribe_pet(animal_type=\\'hamster\\', pet_name=\\'harry\\')\\n```\\n\\n这些函数调用的输出与前面的示例相同。\\n\\n使用哪种调用方式无关紧要。可以使用对你来说最容易理解的调用方式，只要函数调用能生成你期望的输出就好。\\n\\n### 避免实参错误\\n\\n等你开始使用函数后，也许会遇到实参不匹配错误。当你提供的实参多于或少于函数完成工作所需的实参数量时，将出现实参不匹配错误。如果在调用 describe_pet()函数时没有指定任何实参，结果将如何呢？\\n\\n```python\\ndef describe_pet(animal_type, pet_name):\\n    \"\"\" 显示宠物的信息 \"\"\"\\n    print(f\"\\\\nI have a {animal_type}.\")\\n    print(f\"My {animal_type}\\'s name is {pet_name.title()}.\")\\n\\ndescribe_pet()\\n```\\n\\n#### 错误信息的解读\\n\\nPython 发现该函数调用缺少必要的信息，并用 traceback 指出了这一点：\\n\\n```\\nTraceback (most recent call last):\\n  File \"pets.py\", line 6, in <module>\\n    describe_pet()\\n    ^^^^^^^^^^^^^^\\nTypeError: describe_pet() missing 2 required positional arguments: \\'animal_type\\' and \\'pet_name\\'\\n```\\n\\ntraceback 首先指出问题出在什么地方，让我们能够回过头去找出函数调用中的错误。然后，指出导致问题的函数调用。最后，traceback 指出该函数调用缺少两个实参，并指出了相应形参的名称。如果这个函数存储在一个独立的文件中，我们也许无须打开这个文件并查看函数的代码，就能重新正确地编写函数调用。\\n\\nPython 能读取函数的代码，并指出需要为哪些形参提供实参，这为我们提供了极大的帮助。这是应该给变量和函数指定描述性名称的另一个原因：如果这样做了，那么无论对于你，还是可能使用你编写的代码的其他任何人来说，Python 提供的错误消息都将更有帮助性。\\n\\n如果提供的实参太多，将出现类似的 traceback，帮助你确保函数调用和函数定义匹配。\\n\\n## 返回值\\n\\n函数并非总是直接显示输出，它还可以处理一些数据，并返回一个或一组值。函数返回的值称为**返回值**。在函数中，可以使用 return 语句将值返回到调用函数的那行代码。返回值让你能够将程序的大部分繁重工作移到函数中完成，从而简化主程序。\\n\\n### 返回简单的值\\n\\n下面来看一个函数，它接受名和姓并返回标准格式的姓名：\\n\\n#### formatted_name.py 示例\\n\\n```python\\ndef get_formatted_name(first_name, last_name):\\n    \"\"\" 返回标准格式的姓名 \"\"\"\\n    full_name = f\"{first_name} {last_name}\"\\n    return full_name.title()\\n\\nmusician = get_formatted_name(\\'jimi\\', \\'hendrix\\')\\nprint(musician)\\n```\\n\\nget_formatted_name()函数的定义通过形参接受名和姓。它将名和姓合在一起，在中间加上一个空格，并将结果赋给变量 full_name。然后，它将 full_name 的值转换为首字母大写的格式，并将结果返回函数调用行。\\n\\n在调用可以返回值的函数时，需要提供一个变量，以便将返回的值赋给它。这里将返回值赋给了变量 musician。输出为标准格式的姓名：\\n\\n```\\nJimi Hendrix\\n```\\n\\n原本只需编写下面的代码就可以输出这个标准格式的姓名，前面做的工作好像太多了：\\n\\n```python\\nprint(\"Jimi Hendrix\")\\n```\\n\\n你要知道，在需要分别存储大量名和姓的大型程序中，像 get_formatted_name()这样的函数非常有用。你可以分别存储名和姓，每当需要显示姓名时就调用这个函数。\\n\\n### 让实参变成可选的\\n\\n有时候，需要让实参变成可选的，以便使用函数的人只在必要时才提供额外的信息。可以使用默认值来让实参变成可选的。\\n\\n假设要扩展 get_formatted_name()函数，使其除了名和姓之外还可以处理中间名。为此，可将其修改成类似这样：\\n\\n```python\\ndef get_formatted_name(first_name, middle_name, last_name):\\n    \"\"\" 返回标准格式的姓名 \"\"\"\\n    full_name = f\"{first_name} {middle_name} {last_name}\"\\n    return full_name.title()\\n\\nmusician = get_formatted_name(\\'john\\', \\'lee\\', \\'hooker\\')\\nprint(musician)\\n```\\n\\n只要同时提供名、中间名和姓，这个函数就能正确运行。它根据这三部分创建一个字符串，在适当的地方加上空格，并将结果转换为首字母大写的格式：\\n\\n```\\nJohn Lee Hooker\\n```\\n\\n#### 处理可选的中间名\\n\\n然而，并非所有人都有中间名。如果调用这个函数时只提供了名和姓，它将不能正确地运行。为让中间名变成可选的，可给形参 middle_name 指定默认值（空字符串），在用户不提供中间名时不使用这个形参。为了让 get_formatted_name()在没有提供中间名时依然正确运行，可给形参 middle_name 指定默认值（空字符串），并将其移到形参列表的末尾：\\n\\n```python\\ndef get_formatted_name(first_name, last_name, middle_name=\\'\\'):\\n    \"\"\" 返回标准格式的姓名 \"\"\"\\n    if middle_name:\\n        full_name = f\"{first_name} {middle_name} {last_name}\"\\n    else:\\n        full_name = f\"{first_name} {last_name}\"\\n    return full_name.title()\\n\\nmusician = get_formatted_name(\\'jimi\\', \\'hendrix\\')\\nprint(musician)\\n\\nmusician = get_formatted_name(\\'john\\', \\'hooker\\', \\'lee\\')\\nprint(musician)\\n```\\n\\n在这个示例中，姓名是根据三个可能提供的部分创建的。每个人都有名和姓，因此在函数定义中首先列出了这两个形参。中间名是可选的，因此在函数定义中最后列出该形参，并将其默认值设置为空字符串。\\n\\n在函数体中，检查是否提供了中间名。Python 将非空字符串解读为 True，如果在函数调用中提供了中间名，条件测试 if middle_name 将为 True。如果提供了中间名，就将名、中间名和姓合并为姓名，再将其修改为首字母大写的格式，并将结果返回函数调用行。在函数调用行，将返回的值赋给变量 musician。最后，这个变量的值被打印了出来。如果没有提供中间名，middle_name 将为空字符串，导致 if 测试未通过，进而执行 else 代码块：只使用名和姓来生成姓名，并将设置好格式的姓名返回函数调用行。在函数调用行，将返回的值赋给变量 musician。最后，这个变量的值被打印了出来。\\n\\n在调用这个函数时，如果只想指定名和姓，调用起来将非常简单。如果还要指定中间名，就必须确保它是最后一个实参，这样 Python 才能正确地将位置实参关联到形参。\\n\\n这个修改后的版本不仅适用于只有名和姓的人，也适用于还有中间名的人：\\n\\n```\\nJimi Hendrix\\nJohn Lee Hooker\\n```\\n\\n可选值在让函数能够处理各种不同情形的同时，确保函数调用尽可能简单。\\n\\n### 返回字典\\n\\n函数可返回任何类型的值，包括列表和字典等较为复杂的数据结构。例如，下面的函数接受姓名的组成部分，并返回一个表示人的字典：\\n\\n#### person.py 示例\\n\\n```python\\ndef build_person(first_name, last_name):\\n    \"\"\" 返回一个字典，其中包含有关一个人的信息 \"\"\"\\n    person = {\\'first\\': first_name, \\'last\\': last_name}\\n    return person\\n\\nmusician = build_person(\\'jimi\\', \\'hendrix\\')\\nprint(musician)\\n```\\n\\nbuild_person()函数接受名和姓，并将这些值放在字典中。在存储 first_name 的值时，使用的键为\\'first\\'，而在存储 last_name 的值时，使用的键为\\'last\\'。然后，返回表示人的整个字典。在此处，打印这个被返回的值。此时，原来的两项文本信息存储在一个字典中：\\n\\n```\\n{\\'first\\': \\'jimi\\', \\'last\\': \\'hendrix\\'}\\n```\\n\\n#### 扩展字典功能\\n\\n这个函数接受简单的文本信息，并将其放在一个更合适的数据结构中，让你不仅能打印这些信息，还能以其他方式处理它们。当前，字符串\\'jimi\\'和\\'hendrix\\'分别被标记为名和姓。你可以轻松地扩展这个函数，使其接受可选值，如中间名、年龄、职业或其他任何要存储的信息。例如，下面的修改能让你存储年龄：\\n\\n```python\\ndef build_person(first_name, last_name, age=None):\\n    \"\"\" 返回一个字典，其中包含有关一个人的信息 \"\"\"\\n    person = {\\'first\\': first_name, \\'last\\': last_name}\\n    if age:\\n        person[\\'age\\'] = age\\n    return person\\n```\\n\\n在函数定义中，新增了一个可选形参 age，其默认值被设置为特殊值 None（表示变量没有值）。可将 None 视为占位值。在条件测试中，None 相当于 False。如果函数调用中包含形参 age 的值，这个值将被存储到字典中。在任何情况下，这个函数都会存储一个人的姓名，并且可以修改它，使其同时存储有关这个人的其他信息。\\n\\n### 结合使用函数和 while 循环\\n\\n可将函数与本书前面介绍的所有 Python 结构结合起来使用。例如，下面将结合使用 get_formatted_name()函数和 while 循环，以更正规的方式问候用户。下面尝试使用名和姓跟用户打招呼：\\n\\n#### greeter.py 示例\\n\\n```python\\ndef get_formatted_name(first_name, last_name):\\n    \"\"\" 返回规范格式的姓名 \"\"\"\\n    full_name = f\"{first_name} {last_name}\"\\n    return full_name.title()\\n\\n# 这是一个无限循环!\\nwhile True:\\n    print(\"\\\\nPlease tell me your name:\")\\n    f_name = input(\"First name: \")\\n    l_name = input(\"Last name: \")\\n\\n    formatted_name = get_formatted_name(f_name, l_name)\\n    print(f\"\\\\nHello, {formatted_name}!\")\\n```\\n\\n在这个示例中，使用的是 get_formatted_name()的简单版本，不涉及中间名。while 循环让用户输入姓名：提示用户依次输入名和姓。\\n\\n#### 提供退出条件\\n\\n但这个 while 循环存在一个问题：没有定义退出条件。在请用户进行一系列输入时，该在什么地方提供退出途径呢？我们要让用户能够尽可能容易地退出，因此在每次提示用户输入时，都应提供退出途径。使用 break 语句可以在每次提示用户输入时提供退出循环的简单途径：\\n\\n```python\\ndef get_formatted_name(first_name, last_name):\\n    \"\"\" 返回规范格式的姓名 \"\"\"\\n    full_name = f\"{first_name} {last_name}\"\\n    return full_name.title()\\n\\nwhile True:\\n    print(\"\\\\nPlease tell me your name:\")\\n    print(\"(enter \\'q\\' at any time to quit)\")\\n\\n    f_name = input(\"First name: \")\\n    if f_name == \\'q\\':\\n        break\\n\\n    l_name = input(\"Last name: \")\\n    if l_name == \\'q\\':\\n        break\\n\\n    formatted_name = get_formatted_name(f_name, l_name)\\n    print(f\"\\\\nHello, {formatted_name}!\")\\n```\\n\\n我们添加了一条消息来告诉用户如何退出。然后在每次提示用户输入时，都检查他输入的是否是退出值。如果是，就退出循环。现在，这个程序将不断地发出问候，直到用户输入的姓或名为\\'q\\'：\\n\\n```\\nPlease tell me your name:\\n(enter \\'q\\' at any time to quit)\\nFirst name: eric\\nLast name: matthes\\n\\nHello, Eric Matthes!\\n\\nPlease tell me your name:\\n(enter \\'q\\' at any time to quit)\\nFirst name: q\\n```\\n\\n## 传递列表\\n\\n你经常会发现，向函数传递列表很有用，可能是名字列表、数值列表或更复杂的对象列表（如字典）。将列表传递给函数后，函数就能直接访问其内容。下面使用函数来提高处理列表的效率。\\n\\n假设有一个用户列表，而我们要向其中的每个用户发出问候。下面的示例将一个名字列表传递给一个名为 greet_users()的函数，这个函数会向列表中的每个人发出问候：\\n\\n#### greet_users.py 示例\\n\\n```python\\ndef greet_users(names):\\n    \"\"\" 向列表中的每个用户发出简单的问候 \"\"\"\\n    for name in names:\\n        msg = f\"Hello, {name.title()}!\"\\n        print(msg)\\n\\nusernames = [\\'hannah\\', \\'ty\\', \\'margot\\']\\ngreet_users(usernames)\\n```\\n\\n我们将 greet_users()定义成接受一个名字列表，并将其赋给形参 names。这个函数遍历收到的列表，并对其中的每个用户打印一条问候语。在函数外，先定义一个用户列表 usernames，再调用 greet_users()并将这个列表传递给它：\\n\\n```\\nHello, Hannah!\\nHello, Ty!\\nHello, Margot!\\n```\\n\\n输出完全符合预期。每个用户都看到了一条个性化的问候语。每当需要问候一组用户时，都可调用这个函数。\\n\\n### 在函数中修改列表\\n\\n将列表传递给函数后，函数就可以对其进行修改了。在函数中对这个列表所做的任何修改都是永久的，这让你能够高效地处理大量数据。\\n\\n来看一家为用户提交的设计制作 3D 打印模型的公司。需要打印的设计事先存储在一个列表中，打印后将被移到另一个列表中。下面是在不使用函数的情况下模拟这个过程的代码：\\n\\n#### printing_models.py 示例\\n\\n```python\\n# 首先创建一个列表，其中包含一些要打印的设计\\nunprinted_designs = [\\'phone case\\', \\'robot pendant\\', \\'dodecahedron\\']\\ncompleted_models = []\\n\\n# 模拟打印每个设计，直到没有未打印的设计为止\\n# 打印每个设计后，都将其移到列表completed_models中\\nwhile unprinted_designs:\\n    current_design = unprinted_designs.pop()\\n    print(f\"Printing model: {current_design}\")\\n    completed_models.append(current_design)\\n\\n# 显示打印好的所有模型\\nprint(\"\\\\nThe following models have been printed:\")\\nfor completed_model in completed_models:\\n    print(completed_model)\\n```\\n\\n这个程序首先创建一个需要打印的设计列表，以及一个名为 completed_models 的空列表，打印每个设计后都将其移到这个空列表中。只要列表 unprinted_designs 中还有设计，while 循环就模拟打印设计的过程：从该列表末尾删除一个设计，将其赋给变量 current_design，并显示一条消息，指出正在打印当前的设计，再将该设计加入列表 completed_models。循环结束后，显示已打印的所有设计：\\n\\n```\\nPrinting model: dodecahedron\\nPrinting model: robot pendant\\nPrinting model: phone case\\n\\nThe following models have been printed:\\ndodecahedron\\nrobot pendant\\nphone case\\n```\\n\\n#### 使用函数重新组织代码\\n\\n可以重新组织这些代码，编写两个函数，让每个都做一件具体的工作。大部分代码与原来相同，只是结构更为合理。第一个函数负责处理打印设计的工作，第二个概述打印了哪些设计：\\n\\n```python\\ndef print_models(unprinted_designs, completed_models):\\n    \"\"\"\\n    模拟打印每个设计，直到没有未打印的设计为止\\n    打印每个设计后，都将其移到列表completed_models中\\n    \"\"\"\\n    while unprinted_designs:\\n        current_design = unprinted_designs.pop()\\n        print(f\"Printing model: {current_design}\")\\n        completed_models.append(current_design)\\n\\ndef show_completed_models(completed_models):\\n    \"\"\" 显示打印好的所有模型 \"\"\"\\n    print(\"\\\\nThe following models have been printed:\")\\n    for completed_model in completed_models:\\n        print(completed_model)\\n\\nunprinted_designs = [\\'phone case\\', \\'robot pendant\\', \\'dodecahedron\\']\\ncompleted_models = []\\n\\nprint_models(unprinted_designs, completed_models)\\nshow_completed_models(completed_models)\\n```\\n\\n首先，定义函数 print_models()，它包含两个形参：一个需要打印的设计列表和一个打印好的模型列表。给定这两个列表，这个函数模拟打印每个设计的过程：将设计逐个从未打印的设计列表中取出，并加入打印好的模型列表。然后，定义函数 show_completed_models()，它包含一个形参：打印好的模型列表。给定这个列表，函数 show_completed_models()显示打印出来的每个模型的名称。\\n\\n虽然这个程序的输出与未使用函数的版本相同，但是代码更有条理。完成大部分工作的代码被移到了两个函数中，让主程序很容易理解。只要看看主程序，你就能轻松地知道这个程序的功能：\\n\\n```python\\nunprinted_designs = [\\'phone case\\', \\'robot pendant\\', \\'dodecahedron\\']\\ncompleted_models = []\\n\\nprint_models(unprinted_designs, completed_models)\\nshow_completed_models(completed_models)\\n```\\n\\n我们创建了一个未打印的设计列表，以及一个空列表，后者用于存储打印好的模型。接下来，由于已经定义了两个函数，因此只需要调用它们并传入正确的实参即可。我们调用 print_models()并向它传递两个列表。像预期的一样，print_models()模拟了打印设计的过程。接下来，调用 show_completed_models()，并将打印好的模型列表传递给它，让它能够指出打印了哪些模型。描述性的函数名让阅读这些代码的人也能一目了然，虽然其中没有任何注释。\\n\\n相比于没有使用函数的版本，这个程序更容易扩展和维护。如果以后需要打印其他设计，只需再次调用 print_models()即可。如果发现需要对模拟打印的代码进行修改，只需修改这些代码一次，就将影响所有调用该函数的地方。与必须分别修改程序的多个地方相比，这种修改的效率更高。\\n\\n这个程序还演示了一种理念：每个函数都应只负责一项具体工作。用第一个函数打印每个设计，用第二个函数显示打印好的模型，优于使用一个函数完成这两项工作。在编写函数时，如果发现它执行的任务太多，请尝试将这些代码划分到两个函数中。别忘了，总是可以在一个函数中调用另一个函数，这有助于将复杂的任务分解成一系列步骤。\\n\\n### 禁止函数修改列表\\n\\n有时候，需要禁止函数修改列表。假设像前一个示例那样，你有一个未打印的设计列表，并且编写了一个将这些设计移到打印好的模型列表中的函数。你可能会做出这样的决定：即便打印了所有的设计，也要保留原来的未打印的设计列表，作为存档。但由于你将所有的设计都移出了 unprinted_designs，这个列表变成了空的——原来的列表没有了。为了解决这个问题，可向函数传递列表的副本而不是原始列表。这样，函数所做的任何修改都只影响副本，而丝毫不影响原始列表。\\n\\n#### 传递列表副本的方法\\n\\n要将列表的副本传递给函数，可以像下面这样做：\\n\\n```python\\nfunction_name(list_name[:])\\n```\\n\\n切片表示法[:]创建列表的副本。在 printing_models.py 中，如果不想清空未打印的设计列表，可像下面这样调用 print_models()：\\n\\n```python\\nprint_models(unprinted_designs[:], completed_models)\\n```\\n\\nprint_models()函数依然能够完成其工作，因为它获得了所有未打印的设计的名称，但它这次使用的是列表 unprinted_designs 的副本，而不是列表 unprinted_designs 本身。像以前一样，列表 completed_models 将包含打印好的模型的名称，但函数所做的修改不会影响列表 unprinted_designs。\\n\\n虽然向函数传递列表的副本可保留原始列表的内容，但除非有充分的理由，否则还是应该将原始列表传递给函数。这是因为，让函数使用现成的列表可避免花时间和内存创建副本，从而提高效率，在处理大型列表时尤其如此。\\n\\n## 传递任意数量的实参\\n\\n有时候，你预先不知道函数需要接受多少个实参，好在 Python 允许函数从调用语句中收集任意数量的实参。\\n\\n例如一个制作比萨的函数，它需要接受很多配料，但无法预先确定顾客要点多少种配料。下面的函数只有一个形参\\\\*toppings，不管调用语句提供了多少实参，这个形参都会将其收入囊中：\\n\\n#### pizza.py 示例\\n\\n```python\\ndef make_pizza(*toppings):\\n    \"\"\" 打印顾客点的所有配料 \"\"\"\\n    print(toppings)\\n\\nmake_pizza(\\'pepperoni\\')\\nmake_pizza(\\'mushrooms\\', \\'green peppers\\', \\'extra cheese\\')\\n```\\n\\n形参名\\\\*toppings 中的星号让 Python 创建一个名为 toppings 的元组，该元组包含函数收到的所有值。函数体内的函数调用 print()生成的输出证明，Python 既能处理使用一个值调用函数的情形，也能处理使用三个值调用函数的情形。它以类似的方式处理不同的调用。注意，Python 会将实参封装到一个元组中，即便函数只收到一个值也是如此：\\n\\n```\\n(\\'pepperoni\\',)\\n(\\'mushrooms\\', \\'green peppers\\', \\'extra cheese\\')\\n```\\n\\n现在，可以将函数调用 print()替换为一个循环，遍历配料列表并对顾客点的比萨进行描述：\\n\\n```python\\ndef make_pizza(*toppings):\\n    \"\"\" 概述要制作的比萨 \"\"\"\\n    print(\"\\\\nMaking a pizza with the following toppings:\")\\n    for topping in toppings:\\n        print(f\"- {topping}\")\\n\\nmake_pizza(\\'pepperoni\\')\\nmake_pizza(\\'mushrooms\\', \\'green peppers\\', \\'extra cheese\\')\\n```\\n\\n不管收到一个值还是三个值，这个函数都能妥善地处理：\\n\\n```\\nMaking a pizza with the following toppings:\\n- pepperoni\\n\\nMaking a pizza with the following toppings:\\n- mushrooms\\n- green peppers\\n- extra cheese\\n```\\n\\n不管函数收到多少个实参，这种语法都管用。\\n\\n### 结合使用位置实参和任意数量的实参\\n\\n如果要让函数接受不同类型的实参，必须在函数定义中将接纳任意数量实参的形参放在最后。Python 先匹配位置实参和关键字实参，再将余下的实参都收集到最后一个形参中。\\n\\n例如，如果前面的函数还需要一个表示比萨尺寸的形参，必须将该形参放在形参\\\\*toppings 的前面：\\n\\n```python\\ndef make_pizza(size, *toppings):\\n    \"\"\" 概述要制作的比萨 \"\"\"\\n    print(f\"\\\\nMaking a {size}-inch pizza with the following toppings:\")\\n    for topping in toppings:\\n        print(f\"- {topping}\")\\n\\nmake_pizza(16, \\'pepperoni\\')\\nmake_pizza(12, \\'mushrooms\\', \\'green peppers\\', \\'extra cheese\\')\\n```\\n\\n基于上述函数定义，Python 将收到的第一个值赋给形参 size，将其他所有的值都存储在元组 toppings 中。在函数调用中，首先指定表示比萨尺寸的实参，再根据需要指定任意数量的配料。\\n\\n现在，每个比萨都有了尺寸和一系列配料，而且这些信息被按正确的顺序打印出来了——首先是尺寸，然后是配料：\\n\\n```\\nMaking a 16-inch pizza with the following toppings:\\n- pepperoni\\n\\nMaking a 12-inch pizza with the following toppings:\\n- mushrooms\\n- green peppers\\n- extra cheese\\n```\\n\\n注意：你经常会看到通用形参名\\\\*args，它也这样收集任意数量的位置实参。\\n\\n### 使用任意数量的关键字实参\\n\\n有时候，你需要接受任意数量的实参，但预先不知道传递给函数的会是什么样的信息。在这种情况下，可将函数编写成能够接受任意数量的键值对——调用语句提供了多少就接受多少。一个这样的示例是创建用户简介：你知道将收到有关用户的信息，但不确定是什么样的信息。在下面的示例中，build_profile()函数不仅接受名和姓，还接受任意数量的关键字实参：\\n\\n#### user_profile.py 示例\\n\\n```python\\ndef build_profile(first, last, **user_info):\\n    \"\"\" 创建一个字典，其中包含我们知道的有关用户的一切 \"\"\"\\n    user_info[\\'first_name\\'] = first\\n    user_info[\\'last_name\\'] = last\\n    return user_info\\n\\nuser_profile = build_profile(\\'albert\\', \\'einstein\\',\\n                           location=\\'princeton\\',\\n                           field=\\'physics\\')\\nprint(user_profile)\\n```\\n\\nbuild_profile()函数的定义要求提供名和姓，同时允许根据需要提供任意数量的名值对。形参\\\\*\\\\*user_info 中的两个星号让 Python 创建一个名为 user_info 的字典，该字典包含函数收到的其他所有名值对。在这个函数中，可以像访问其他字典那样访问 user_info 中的名值对。\\n\\n在 build_profile()的函数体内，将名和姓加入字典 user_info，因为总是会从用户那里收到这两项信息，而这两项信息还没被放在字典中。接下来，将字典 user_info 返回函数调用行。\\n\\n我们调用 build_profile()，向它传递名（\\'albert\\'）、姓（\\'einstein\\'）和两个键值对（location=\\'princeton\\'和 field=\\'physics\\'），并将返回的 user_info 赋给变量 user_profile，再打印这个变量：\\n\\n```\\n{\\'location\\': \\'princeton\\', \\'field\\': \\'physics\\', \\'first_name\\': \\'albert\\', \\'last_name\\': \\'einstein\\'}\\n```\\n\\n在这里，返回的字典包含用户的名和姓，还有居住地和研究领域。在调用这个函数时，不管额外提供多少个键值对，它都能正确地处理。\\n\\n在编写函数时，可以用各种方式混合使用位置实参、关键字实参和任意数量的实参。知道这些实参类型大有裨益，因为你在阅读别人编写的代码时经常会见到它们。要正确地使用这些类型的实参并知道使用它们的时机，需要一定的练习。就目前而言，牢记使用最简单的方法来完成任务就好了。继续往下阅读，你就会知道在各种情况下使用哪种方法的效率最高。\\n\\n注意：你经常会看到形参名\\\\*\\\\*kwargs，它用于收集任意数量的关键字实参。\\n\\n## 将函数存储在模块中\\n\\n使用函数的优点之一是可将代码块与主程序分离。通过给函数指定描述性名称，能让程序容易理解得多。你还可以更进一步，将函数存储在称为**模块**的独立文件中，再将模块导入（import）主程序。import 语句可让你在当前运行的程序文件中使用模块中的代码。\\n\\n通过将函数存储在独立的文件中，可隐藏程序代码的细节，将重点放在程序的高层逻辑上。这还能让你在众多不同的程序中复用函数。将函数存储在独立文件中后，可与其他程序员共享这些文件而不是整个程序。知道如何导入函数还能让你使用其他程序员编写的函数库。\\n\\n导入模块的方法有好几种，下面对每种都做简要的介绍。\\n\\n### 导入整个模块\\n\\n要让函数是可导入的，得先创建模块。**模块**是扩展名为.py 的文件，包含要导入程序的代码。下面来创建一个包含 make_pizza()函数的模块。为此，将文件 pizza.py 中除了函数 make_pizza()之外的代码删除：\\n\\n#### pizza.py 模块\\n\\n```python\\ndef make_pizza(size, *toppings):\\n    \"\"\" 概述要制作的比萨 \"\"\"\\n    print(f\"\\\\nMaking a {size}-inch pizza with the following toppings:\")\\n    for topping in toppings:\\n        print(f\"- {topping}\")\\n```\\n\\n接下来，在 pizza.py 所在的目录中创建一个名为 making_pizzas.py 的文件。这个文件先导入刚创建的模块，再调用 make_pizza()两次：\\n\\n#### making_pizzas.py 示例\\n\\n```python\\nimport pizza\\n\\npizza.make_pizza(16, \\'pepperoni\\')\\npizza.make_pizza(12, \\'mushrooms\\', \\'green peppers\\', \\'extra cheese\\')\\n```\\n\\n当 Python 读取这个文件时，代码行 import pizza 会让 Python 打开文件 pizza.py，并将其中的所有函数都复制到这个程序中。你看不到复制代码的过程，因为 Python 会在程序即将运行时在幕后复制这些代码。你只需要知道，在 making_pizzas.py 中，可使用 pizza.py 中定义的所有函数。\\n\\n要调用被导入模块中的函数，可指定被导入模块的名称 pizza 和函数名 make_pizza()，并用句点隔开。这些代码的输出与没有导入模块的原始程序相同：\\n\\n```\\nMaking a 16-inch pizza with the following toppings:\\n- pepperoni\\n\\nMaking a 12-inch pizza with the following toppings:\\n- mushrooms\\n- green peppers\\n- extra cheese\\n```\\n\\n这就是一种导入方法：只需编写一条 import 语句并在其中指定模块名，就可在程序中使用该模块中的所有函数。如果使用这种 import 语句导入了名为 module_name.py 的整个模块，就可使用下面的语法来使用其中的任意一个函数：\\n\\n```python\\nmodule_name.function_name()\\n```\\n\\n### 导入特定的函数\\n\\n还可以只导入模块中的特定函数，语法如下：\\n\\n```python\\nfrom module_name import function_name\\n```\\n\\n用逗号分隔函数名，可根据需要从模块中导入任意数量的函数：\\n\\n```python\\nfrom module_name import function_0, function_1, function_2\\n```\\n\\n对于前面的 making_pizzas.py 示例，如果只想导入要使用的函数，代码将类似于下面这样：\\n\\n```python\\nfrom pizza import make_pizza\\n\\nmake_pizza(16, \\'pepperoni\\')\\nmake_pizza(12, \\'mushrooms\\', \\'green peppers\\', \\'extra cheese\\')\\n```\\n\\n如果使用这种语法，在调用函数时则无须使用句点。由于在 import 语句中显式地导入了 make_pizza()函数，因此在调用时只需指定其名称即可。\\n\\n### 使用 as 给函数指定别名\\n\\n如果要导入的函数的名称太长或者可能与程序中既有的名称冲突，可指定简短而独一无二的**别名**（alias）：函数的另一个名称，类似于外号。要给函数指定这种特殊的外号，需要在导入时这样做。\\n\\n下面给 make_pizza()函数指定了别名 mp()。这是在 import 语句中使用 make_pizza as mp 实现的，关键字 as 将函数重命名为指定的别名：\\n\\n```python\\nfrom pizza import make_pizza as mp\\n\\nmp(16, \\'pepperoni\\')\\nmp(12, \\'mushrooms\\', \\'green peppers\\', \\'extra cheese\\')\\n```\\n\\n上面的 import 语句将函数 make_pizza()重命名为 mp()。在这个程序中，每当需要调用 make_pizza()时，都可将其简写成 mp()。Python 将运行 make_pizza()中的代码，同时避免与程序可能包含的 make_pizza()函数混淆。\\n\\n指定别名的通用语法如下：\\n\\n```python\\nfrom module_name import function_name as fn\\n```\\n\\n### 使用 as 给模块指定别名\\n\\n还可以给模块指定别名。通过给模块指定简短的别名（如给 pizza 模块指定别名 p），你能够更轻松地调用模块中的函数。相比于 pizza.make_pizza()，p.make_pizza()显然更加简洁：\\n\\n```python\\nimport pizza as p\\n\\np.make_pizza(16, \\'pepperoni\\')\\np.make_pizza(12, \\'mushrooms\\', \\'green peppers\\', \\'extra cheese\\')\\n```\\n\\n上述 import 语句给 pizza 模块指定了别名 p，但该模块中所有函数的名称都没变。要调用 make_pizza()函数，可将其写为 p.make_pizza()而不是 pizza.make_pizza()。这样不仅让代码更加简洁，还让你不用再关注模块名，只专注于描述性的函数名。这些函数名明确地指出了函数的功能，对于理解代码来说，它们比模块名更重要。\\n\\n给模块指定别名的通用语法如下：\\n\\n```python\\nimport module_name as mn\\n```\\n\\n### 导入模块中的所有函数\\n\\n使用星号（\\\\*）运算符可让 Python 导入模块中的所有函数：\\n\\n```python\\nfrom pizza import *\\n\\nmake_pizza(16, \\'pepperoni\\')\\nmake_pizza(12, \\'mushrooms\\', \\'green peppers\\', \\'extra cheese\\')\\n```\\n\\nimport 语句中的星号让 Python 将模块 pizza 中的每个函数都复制到这个程序文件中。由于导入了每个函数，可通过名称来调用每个函数，无须使用**点号**（dot notation）。然而，在使用并非自己编写的大型模块时，最好不要使用这种导入方法，因为如果模块中有函数的名称与当前项目中既有的名称相同，可能导致意想不到的结果：Python 可能会因为遇到多个名称相同的函数或变量而覆盖函数，而不是分别导入所有的函数。\\n\\n最佳的做法是，要么只导入需要使用的函数，要么导入整个模块并使用点号。这都能让代码更清晰，更容易阅读和理解。这里之所以介绍导入模块中所有函数的方法，只是想让你在阅读别人编写的代码时，能够理解类似于下面的 import 语句：\\n\\n```python\\nfrom module_name import *\\n```\\n\\n## 函数编写指南\\n\\n在编写函数时，需要牢记几个细节。应给函数指定描述性名称，且只使用小写字母和下划线。描述性名称可帮助你和别人明白代码想要做什么。在给模块命名时也应遵循上述约定。\\n\\n每个函数都应包含简要阐述其功能的注释。该注释应紧跟在函数定义后面，并采用文档字符串的格式。这样，其他程序员只需阅读文档字符串中的描述就能够使用它：他们完全可以相信代码会如描述的那样运行，并且只要知道函数名、需要的实参以及返回值的类型，就能在自己的程序中使用它。\\n\\n### 格式规范\\n\\n在给形参指定默认值时，等号两边不要有空格：\\n\\n```python\\ndef function_name(parameter_0, parameter_1=\\'default value\\')\\n```\\n\\n函数调用中的关键字实参也应遵循这种约定：\\n\\n```python\\nfunction_name(value_0, parameter_1=\\'value\\')\\n```\\n\\nPEP 8 建议代码行的长度不要超过 79 个字符。这样，只要编辑器窗口适中，就能看到整行代码。如果形参很多，导致函数定义的长度超过了 79 个字符，可在函数定义中输入左括号后按回车键，并在下一行连按两次制表符键，从而将形参列表和只缩进一层的函数体区分开来。\\n\\n大多数编辑器会自动对齐后续参数列表行，使其缩进程度与你给第一个参数列表行指定的缩进程度相同：\\n\\n```python\\ndef function_name(\\n        parameter_0, parameter_1, parameter_2,\\n        parameter_3, parameter_4, parameter_5):\\n    function body...\\n```\\n\\n如果程序或模块包含多个函数，可使用两个空行将相邻的函数分开。这样将更容易知道前一个函数到什么地方结束，下一个函数从什么地方开始。\\n\\n所有的 import 语句都应放在文件开头。唯一的例外是，你要在文件开头使用注释来描述整个程序。\\n\\n## 小结\\n\\n在本章中，你首先学习了如何编写函数，以及如何传递实参，让函数能够访问完成工作所需的信息。然后学习了如何使用位置实参和关键字实参，以及如何接受任意数量的实参。你见识了显示输出的函数和返回值的函数，知道了如何将函数与列表、字典、if 语句和 while 循环结合起来使用，以及如何将函数存储在称为模块的独立文件中，让程序文件更简单、更易于理解。最后，你了解了函数编写指南，遵循这些指南可让程序始终保持良好的结构，对你和其他人来说都易于阅读。\\n\\n程序员的目标之一是编写简单的代码来完成任务，而函数有助于实现这样的目标。使用它们，你在编写好一个个代码块并确定其能够正确运行后，就可不必在上面花更多精力。确定函数能够正确地完成工作后，你就可以接着投身于下一个编程任务，因为你知道它们以后也不会出问题。\\n\\n函数让你在编写一次代码后，可以复用它们任意多次。当需要运行函数中的代码时，只需编写一行函数调用代码，就能让函数完成其工作。当需要修改函数的行为时，只需修改一个代码块，你所做的修改就将影响调用这个函数的每个地方。\\n\\n使用函数让程序更容易阅读，而良好的函数名概述了程序各个部分的作用。相比于阅读一系列代码块，阅读一系列函数调用让你能够更快地明白程序的作用。\\n\\n函数还让代码更容易测试和调试。如果程序使用一系列函数来完成任务，其中的每个函数都完成一项具体工作，那么程序测试和维护起来将容易得多：可编写分别调用每个函数的程序，并测试每个函数是否在可能的各种情形下都能正确地运行。经过这样的测试，你就能深信每次调用这些函数时，它们都将正确地运行。\\n\\n在第 9 章中，你将学习编写类。类将函数和数据整洁地封装起来，让你能够灵活而高效地使用它们。\\n', path=None, url=None, mimetype=None), image_resource=None, audio_resource=None, video_resource=None, text_template='{metadata_str}\\n\\n{content}')"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# =============================\n", "# 2) 读取 & 切块 --> 节点\n", "# =============================\n", "from llama_index.core import SimpleDirectoryReader\n", "\n", "# ⭐ 关键三行（按你的要求保持原样）\n", "documents = SimpleDirectoryReader('../llama_data').load_data()\n", "# 给每个文档加上示例 metadata\n", "for idx, doc in enumerate(documents, start=1):\n", "    doc.metadata[\"course_id\"] = \"course_01\"  # 真实场景可根据文件夹层级自动填\n", "    doc.metadata[\"course_material_id\"] = f\"material_{idx:03d}\"\n", "    \n", "documents[0]  # 看看结构"]}, {"cell_type": "code", "execution_count": 3, "id": "408d327b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["总共生成了 37 个节点\n", "================================================================================\n", "节点 1:\n", "# Python 第八章 函数\n", "\n", "在本章中，你将学习编写**函数**（function）。函数是带名字的代码块，用于完成具体的工作。要执行函数定义的特定任务，可**调用**（call）该函数。当需要在程序中多次执行同一项任务时，无须反复编写完成该任务的代码，只需要调用执行该任务的函数，让 Python 运行其中的代码即可。你将发现，使用函数，程序编写、阅读、测试和修复起来都会更容易。\n", "\n", "你还将学习各种向函数传递信息的方式，学习编写主要任务是显示信息的函数，以及用于处理数据并返回一个或一组值的函数。最后，你将学习如何将函数存储在称为**模块**（module）的独立文件中，让主程序文件更加整洁。\n", "\n", "## 定义函数\n", "\n", "下面是一个打印问候语的简单函数，名为 greet_user()：\n", "\n", "### greeter.py 示例\n", "\n", "```python\n", "def greet_user():\n", "    \"\"\" 显示简单的问候语 \"\"\"\n", "    print(\"Hello!\")\n", "字符数量: 432\n", "================================================================================\n", "节点 2:\n", "greet_user()\n", "```\n", "\n", "#### 函数定义的基本结构\n", "\n", "这个示例演示了最简单的函数结构。第一行代码使用关键字 def 来告诉 Python，你要定义一个函数。这是**函数定义**，向 Python 指出了函数名，还可以在括号内指出函数为完成任务需要什么样的信息。在这里，函数名为 greet_user()，它不需要任何信息就能完成工作，因此括号内是空的（即便如此，括号也必不可少）。最后，定义以冒号结尾。\n", "\n", "#### 函数体和文档字符串\n", "\n", "紧跟在 def greet_user():后面的所有缩进行构成了函数体。第二行的文本是称为**文档字符串**（docstring）的注释，描述了函数是做什么的。Python 在为程序中的函数生成文档时，会查找紧跟在函数定义后的字符串。这些字符串通常前后分别用三个双引号引起，能够包含多行。\n", "\n", "代码行 print(\"Hello!\")是函数体内的唯一行代码，因此 greet_user()只做一项工作：打印 Hello!。\n", "\n", "#### 函数调用\n", "\n", "要使用这个函数，必须调用它。**函数调用**让 Python 执行函数中的代码。要调用函数，可依次指定函数名以及用括号括起的必要信息。由于这个函数不需要任何信息，调用它时只需输入 greet_user()即可。和预期的一样，它会打印 Hello!：\n", "\n", "```\n", "Hello!\n", "字符数量: 584\n", "================================================================================\n", "节点 3:\n", "```\n", "\n", "### 向函数传递信息\n", "\n", "只需稍作修改，就可让 greet_user()函数在问候用户时以其名字作为抬头。为此，可在函数定义 def greet_user()的括号内添加 username。这样，可让函数接受你给 username 指定的任何值。现在，这个函数要求你在调用它时给 username 指定一个值。因此在调用 greet_user()时，可将一个名字传递给它，如下所示：\n", "\n", "```python\n", "def greet_user(username):\n", "    \"\"\" 显示简单的问候语 \"\"\"\n", "    print(f\"Hello, {username.title()}!\")\n", "\n", "greet_user('jesse')\n", "```\n", "\n", "#### 函数调用的结果\n", "\n", "代码 greet_user('jesse')调用函数 greet_user()，并向它提供执行函数调用 print()所需的信息。这个函数接受你传递给它的名字，并向这个人发出问候：\n", "\n", "```\n", "Hello, <PERSON>!\n", "```\n", "\n", "同样，greet_user('sarah')调用函数 greet_user()并向它传递'sarah'，从而打印 Hello, Sarah!。你可以根据需要调用函数 greet_user()任意多次，无论在调用时传入什么名字，都将生成相应的输出。\n", "\n", "### 实参和形参\n", "\n", "前面在定义 greet_user()函数时，要求给变量 username 指定一个值。这样，在调用这个函数并提供这种信息（人名）时，它将打印相应的问候语。\n", "\n", "#### 形参的概念\n", "\n", "在 greet_user()函数的定义中，变量 username 是一个**形参**（parameter），即函数完成工作所需的信息。\n", "字符数量: 745\n", "================================================================================\n", "节点 4:\n", "#### 形参的概念\n", "\n", "在 greet_user()函数的定义中，变量 username 是一个**形参**（parameter），即函数完成工作所需的信息。\n", "\n", "#### 实参的概念\n", "\n", "在代码 greet_user('jesse')中，值'jesse'是一个**实参**（argument），即在调用函数时传递给函数的信息。在调用函数时，我们将要让函数使用的信息放在括号内。在 greet_user('jesse')这个示例中，我们将实参'jesse'传递给函数 greet_user()，这个值被赋给了形参 username。\n", "\n", "注意：大家有时候会形参、实参不分。即使你看到有人将函数定义中的变量称为实参或将函数调用中的变量称为形参，也不要大惊小怪。\n", "\n", "## 传递实参\n", "\n", "函数定义中可能包含多个形参，因此函数调用中也可能包含多个实参。向函数传递实参的方式很多：既可以使用**位置实参**，这要求实参的顺序与形参的顺序相同；也可以使用**关键字实参**，其中每个实参都由变量名和值组成；还可以使用列表和字典。下面依次介绍这些方式。\n", "\n", "### 位置实参\n", "\n", "在调用函数时，Python 必须将函数调用中的每个实参关联到函数定义中的一个形参。最简单的方式是基于实参的顺序进行关联。以这种方式关联的实参称为**位置实参**。\n", "\n", "为了明白其中的工作原理，我们来看一个显示宠物信息的函数。\n", "字符数量: 588\n", "================================================================================\n", "节点 5:\n", "以这种方式关联的实参称为**位置实参**。\n", "\n", "为了明白其中的工作原理，我们来看一个显示宠物信息的函数。这个函数指出一个宠物属于哪种动物以及它叫什么名字，如下所示：\n", "\n", "#### pets.py 示例\n", "\n", "```python\n", "def describe_pet(animal_type, pet_name):\n", "    \"\"\" 显示宠物的信息 \"\"\"\n", "    print(f\"\\nI have a {animal_type}.\")\n", "    print(f\"My {animal_type}'s name is {pet_name.title()}.\")\n", "\n", "describe_pet('hamster', 'harry')\n", "```\n", "\n", "这个函数的定义表明，它需要一个动物类型和一个名字。在调用 describe_pet()时，需要按顺序提供一个动物类型和一个名字。例如，在刚才的函数调用中，实参'hamster'被赋给形参 animal_type，而实参'harry'被赋给形参 pet_name。在函数体内，使用这两个形参来显示宠物的信息。\n", "\n", "输出描述了一只名为 Harry 的仓鼠：\n", "\n", "```\n", "I have a hamster.\n", "My hamster's name is <PERSON>.\n", "```\n", "\n", "#### 调用函数多次\n", "\n", "可根据需要调用函数任意多次。要再描述一个宠物，只需再次调用 describe_pet()即可：\n", "\n", "```python\n", "def describe_pet(animal_type, pet_name):\n", "    \"\"\" 显示宠物的信息 \"\"\"\n", "    print(f\"\\nI have a {animal_type}.\")\n", "    print(f\"My {animal_type}'s name is {pet_name.title()}.\")\n", "字符数量: 776\n", "================================================================================\n", "节点 6:\n", "print(f\"My {animal_type}'s name is {pet_name.title()}.\")\n", "\n", "describe_pet('hamster', 'harry')\n", "describe_pet('dog', 'willie')\n", "```\n", "\n", "第二次调用 describe_pet()函数时，向它传递实参'dog'和'willie'。与第一次调用时一样，Python 将实参'dog'关联到形参 animal_type，并将实参'willie'关联到形参 pet_name。\n", "\n", "与前面一样，这个函数完成了任务，但打印的是一条名为 Willie 的小狗的信息。至此，有一只名为 Harry 的仓鼠，还有一条名为 Willie 的小狗：\n", "\n", "```\n", "I have a hamster.\n", "My hamster's name is <PERSON>.\n", "\n", "I have a dog.\n", "My dog's name is <PERSON>.\n", "```\n", "\n", "多次调用同一个函数是一种效率极高的工作方式。只需在函数中编写一次描述宠物的代码，每当需要描述新宠物时，就都可以调用这个函数并向它提供新宠物的信息。即便描述宠物的代码增加到了 10 行，依然只需使用一行调用函数的代码，就可以描述一个新宠物。\n", "\n", "在函数中，可根据需要使用任意数量的位置实参，Python 将按顺序将函数调用中的实参关联到函数定义中相应的形参。\n", "\n", "#### 位置实参的顺序很重要\n", "\n", "当使用位置实参来调用函数时，如果实参的顺序不正确，结果可能会出乎意料：\n", "\n", "```python\n", "def describe_pet(animal_type, pet_name):\n", "    \"\"\" 显示宠物的信息 \"\"\"\n", "    print(f\"\\nI have a {animal_type}.\")\n", "    print(f\"My {animal_type}'s name is {pet_name.title()}.\")\n", "字符数量: 821\n", "================================================================================\n", "节点 7:\n", "print(f\"My {animal_type}'s name is {pet_name.title()}.\")\n", "\n", "describe_pet('harry', 'hamster')\n", "```\n", "\n", "在这个函数调用中，先指定名字，再指定动物类型。由于实参'harry'在前，这个值将被赋给形参 animal_type，而后面的'hamster'将被赋给形参 pet_name。结果是有一个名为 Hamster 的 harry：\n", "\n", "```\n", "I have a harry.\n", "My harry's name is <PERSON><PERSON>.\n", "```\n", "\n", "如果你得到的结果像上面一样可笑，请确认函数调用中实参的顺序与函数定义中形参的顺序是否一致。\n", "\n", "### 关键字实参\n", "\n", "**关键字实参**是传递给函数的名值对。这样会直接在实参中将名称和值关联起来，因此向函数传递实参时就不会混淆了（不会得到名为 Hamster 的 harry 这样的结果）。关键字实参不仅让你无须考虑函数调用中的实参顺序，而且清楚地指出了函数调用中各个值的用途。\n", "\n", "下面重新编写 pets.py，在其中使用关键字实参来调用 describe_pet()：\n", "\n", "```python\n", "def describe_pet(animal_type, pet_name):\n", "    \"\"\" 显示宠物的信息 \"\"\"\n", "    print(f\"\\nI have a {animal_type}.\")\n", "    print(f\"My {animal_type}'s name is {pet_name.title()}.\")\n", "字符数量: 671\n", "================================================================================\n", "节点 8:\n", "print(f\"My {animal_type}'s name is {pet_name.title()}.\")\n", "\n", "describe_pet(animal_type='hamster', pet_name='harry')\n", "```\n", "\n", "#### 关键字实参的优势\n", "\n", "describe_pet()函数还和之前一样，但这次调用这个函数时，向 Python 明确地指出了各个实参对应的形参。当看到这个函数调用时，Python 知道应该将实参'hamster'和'harry'分别赋给形参 animal_type 和 pet_name。输出正确无误，指出有一只名为 Harry 的仓鼠。\n", "\n", "关键字实参的顺序无关紧要，因为 Python 知道各个值该被赋给哪个形参。下面两个函数调用是等效的：\n", "\n", "```python\n", "describe_pet(animal_type='hamster', pet_name='harry')\n", "describe_pet(pet_name='harry', animal_type='hamster')\n", "```\n", "\n", "注意：在使用关键字实参时，务必准确地指定函数定义中的形参名。\n", "\n", "### 默认值\n", "\n", "在编写函数时，可以给每个形参指定**默认值**。如果在调用函数中给形参提供了实参，Python 将使用指定的实参值；否则，将使用形参的默认值。因此，给形参指定默认值后，可在函数调用中省略相应的实参。使用默认值不仅能简化函数调用，还能清楚地指出函数的典型用法。\n", "\n", "如果你发现在调用 describe_pet()时，描述的大多是小狗，就可将形参 animal_type 的默认值设置为'dog'。这样，当调用 describe_pet()来描述小狗时，就可以不提供该信息：\n", "\n", "```python\n", "def describe_pet(pet_name, animal_type='dog'):\n", "    \"\"\" 显示宠物的信息 \"\"\"\n", "    print(f\"\\nI have a {animal_type}.\")\n", "字符数量: 858\n", "================================================================================\n", "节点 9:\n", "print(f\"My {animal_type}'s name is {pet_name.title()}.\")\n", "\n", "describe_pet(pet_name='willie')\n", "```\n", "\n", "#### 默认值的使用规则\n", "\n", "这里修改了 describe_pet()函数的定义，在其中给形参 animal_type 指定了默认值'dog'。这样，在调用这个函数时，如果没有给 animal_type 指定值，Python 将自动把这个形参设置为'dog'：\n", "\n", "```\n", "I have a dog.\n", "My dog's name is <PERSON>.\n", "```\n", "\n", "请注意，在这个函数的定义中，修改了形参的排列顺序。由于给 animal_type 指定了默认值，无须通过实参来指定动物类型，因此函数调用只包含一个实参——宠物的名字。然而，Python 依然将这个实参视为位置实参，如果函数调用只包含宠物的名字，这个实参将被关联到函数定义中的第一个形参。这就是需要将 pet_name 放在形参列表开头的原因。\n", "\n", "现在，使用这个函数的最简单方式是，在函数调用中只提供小狗的名字：\n", "\n", "```python\n", "describe_pet('willie')\n", "```\n", "\n", "这个函数调用的输出与前一个示例相同。只提供了一个实参'willie'，这个实参将被关联到函数定义中的第一个形参 pet_name。由于没有给 animal_type 提供实参，因此 Python 使用默认值'dog'。\n", "\n", "如果要描述的动物不是小狗，可使用类似于下面的函数调用：\n", "\n", "```python\n", "describe_pet(pet_name='harry', animal_type='hamster')\n", "```\n", "\n", "由于显式地给 animal_type 提供了实参，Python 将忽略这个形参的默认值。\n", "字符数量: 773\n", "================================================================================\n", "节点 10:\n", "animal_type='hamster')\n", "```\n", "\n", "由于显式地给 animal_type 提供了实参，Python 将忽略这个形参的默认值。\n", "\n", "注意：当使用默认值时，必须在形参列表中先列出没有默认值的形参，再列出有默认值的形参。这让 Python 依然能够正确地解读位置实参。\n", "\n", "### 等效的函数调用\n", "\n", "鉴于可混合使用位置实参、关键字实参和默认值，通常有多种等效的函数调用方式。请看 describe_pet()函数的如下定义，其中给一个形参提供了默认值：\n", "\n", "```python\n", "def describe_pet(pet_name, animal_type='dog'):\n", "```\n", "\n", "基于这种定义，在任何情况下都必须给 pet_name 提供实参。在指定该实参时，既可以使用位置实参，也可以使用关键字实参。如果要描述的动物不是小狗，还必须在函数调用中给 animal_type 提供实参。同样，在指定该实参时，既可以使用位置实参，也可以使用关键字实参。\n", "\n", "下面对这个函数的所有调用都可行：\n", "\n", "```python\n", "# 一条名为Willie的小狗\n", "describe_pet('willie')\n", "describe_pet(pet_name='willie')\n", "\n", "# 一只名为Harry的仓鼠\n", "describe_pet('harry', 'hamster')\n", "describe_pet(pet_name='harry', animal_type='hamster')\n", "describe_pet(animal_type='hamster', pet_name='harry')\n", "```\n", "\n", "这些函数调用的输出与前面的示例相同。\n", "\n", "使用哪种调用方式无关紧要。可以使用对你来说最容易理解的调用方式，只要函数调用能生成你期望的输出就好。\n", "字符数量: 762\n", "================================================================================\n", "节点 11:\n", "使用哪种调用方式无关紧要。可以使用对你来说最容易理解的调用方式，只要函数调用能生成你期望的输出就好。\n", "\n", "### 避免实参错误\n", "\n", "等你开始使用函数后，也许会遇到实参不匹配错误。当你提供的实参多于或少于函数完成工作所需的实参数量时，将出现实参不匹配错误。如果在调用 describe_pet()函数时没有指定任何实参，结果将如何呢？\n", "\n", "```python\n", "def describe_pet(animal_type, pet_name):\n", "    \"\"\" 显示宠物的信息 \"\"\"\n", "    print(f\"\\nI have a {animal_type}.\")\n", "    print(f\"My {animal_type}'s name is {pet_name.title()}.\")\n", "\n", "describe_pet()\n", "```\n", "\n", "#### 错误信息的解读\n", "\n", "Python 发现该函数调用缺少必要的信息，并用 traceback 指出了这一点：\n", "\n", "```\n", "Traceback (most recent call last):\n", "  File \"pets.py\", line 6, in <module>\n", "    describe_pet()\n", "    ^^^^^^^^^^^^^^\n", "TypeError: describe_pet() missing 2 required positional arguments: 'animal_type' and 'pet_name'\n", "```\n", "\n", "traceback 首先指出问题出在什么地方，让我们能够回过头去找出函数调用中的错误。然后，指出导致问题的函数调用。最后，traceback 指出该函数调用缺少两个实参，并指出了相应形参的名称。如果这个函数存储在一个独立的文件中，我们也许无须打开这个文件并查看函数的代码，就能重新正确地编写函数调用。\n", "\n", "Python 能读取函数的代码，并指出需要为哪些形参提供实参，这为我们提供了极大的帮助。\n", "字符数量: 829\n", "================================================================================\n", "节点 12:\n", "Python 能读取函数的代码，并指出需要为哪些形参提供实参，这为我们提供了极大的帮助。这是应该给变量和函数指定描述性名称的另一个原因：如果这样做了，那么无论对于你，还是可能使用你编写的代码的其他任何人来说，Python 提供的错误消息都将更有帮助性。\n", "\n", "如果提供的实参太多，将出现类似的 traceback，帮助你确保函数调用和函数定义匹配。\n", "\n", "## 返回值\n", "\n", "函数并非总是直接显示输出，它还可以处理一些数据，并返回一个或一组值。函数返回的值称为**返回值**。在函数中，可以使用 return 语句将值返回到调用函数的那行代码。返回值让你能够将程序的大部分繁重工作移到函数中完成，从而简化主程序。\n", "\n", "### 返回简单的值\n", "\n", "下面来看一个函数，它接受名和姓并返回标准格式的姓名：\n", "\n", "#### formatted_name.py 示例\n", "\n", "```python\n", "def get_formatted_name(first_name, last_name):\n", "    \"\"\" 返回标准格式的姓名 \"\"\"\n", "    full_name = f\"{first_name} {last_name}\"\n", "    return full_name.title()\n", "\n", "musician = get_formatted_name('jimi', 'hendrix')\n", "print(musician)\n", "```\n", "\n", "get_formatted_name()函数的定义通过形参接受名和姓。它将名和姓合在一起，在中间加上一个空格，并将结果赋给变量 full_name。然后，它将 full_name 的值转换为首字母大写的格式，并将结果返回函数调用行。\n", "\n", "在调用可以返回值的函数时，需要提供一个变量，以便将返回的值赋给它。\n", "字符数量: 743\n", "================================================================================\n", "节点 13:\n", "在调用可以返回值的函数时，需要提供一个变量，以便将返回的值赋给它。这里将返回值赋给了变量 musician。输出为标准格式的姓名：\n", "\n", "```\n", "<PERSON><PERSON>\n", "```\n", "\n", "原本只需编写下面的代码就可以输出这个标准格式的姓名，前面做的工作好像太多了：\n", "\n", "```python\n", "print(\"<PERSON><PERSON>\")\n", "```\n", "\n", "你要知道，在需要分别存储大量名和姓的大型程序中，像 get_formatted_name()这样的函数非常有用。你可以分别存储名和姓，每当需要显示姓名时就调用这个函数。\n", "\n", "### 让实参变成可选的\n", "\n", "有时候，需要让实参变成可选的，以便使用函数的人只在必要时才提供额外的信息。可以使用默认值来让实参变成可选的。\n", "\n", "假设要扩展 get_formatted_name()函数，使其除了名和姓之外还可以处理中间名。为此，可将其修改成类似这样：\n", "\n", "```python\n", "def get_formatted_name(first_name, middle_name, last_name):\n", "    \"\"\" 返回标准格式的姓名 \"\"\"\n", "    full_name = f\"{first_name} {middle_name} {last_name}\"\n", "    return full_name.title()\n", "\n", "musician = get_formatted_name('john', 'lee', 'hooker')\n", "print(musician)\n", "```\n", "\n", "只要同时提供名、中间名和姓，这个函数就能正确运行。它根据这三部分创建一个字符串，在适当的地方加上空格，并将结果转换为首字母大写的格式：\n", "\n", "```\n", "<PERSON>\n", "```\n", "\n", "#### 处理可选的中间名\n", "\n", "然而，并非所有人都有中间名。\n", "字符数量: 764\n", "================================================================================\n", "节点 14:\n", "如果调用这个函数时只提供了名和姓，它将不能正确地运行。为让中间名变成可选的，可给形参 middle_name 指定默认值（空字符串），在用户不提供中间名时不使用这个形参。为了让 get_formatted_name()在没有提供中间名时依然正确运行，可给形参 middle_name 指定默认值（空字符串），并将其移到形参列表的末尾：\n", "\n", "```python\n", "def get_formatted_name(first_name, last_name, middle_name=''):\n", "    \"\"\" 返回标准格式的姓名 \"\"\"\n", "    if middle_name:\n", "        full_name = f\"{first_name} {middle_name} {last_name}\"\n", "    else:\n", "        full_name = f\"{first_name} {last_name}\"\n", "    return full_name.title()\n", "\n", "musician = get_formatted_name('jimi', 'hendrix')\n", "print(musician)\n", "\n", "musician = get_formatted_name('john', 'hooker', 'lee')\n", "print(musician)\n", "```\n", "\n", "在这个示例中，姓名是根据三个可能提供的部分创建的。每个人都有名和姓，因此在函数定义中首先列出了这两个形参。中间名是可选的，因此在函数定义中最后列出该形参，并将其默认值设置为空字符串。\n", "\n", "在函数体中，检查是否提供了中间名。Python 将非空字符串解读为 True，如果在函数调用中提供了中间名，条件测试 if middle_name 将为 True。如果提供了中间名，就将名、中间名和姓合并为姓名，再将其修改为首字母大写的格式，并将结果返回函数调用行。在函数调用行，将返回的值赋给变量 musician。最后，这个变量的值被打印了出来。\n", "字符数量: 844\n", "================================================================================\n", "节点 15:\n", "在函数调用行，将返回的值赋给变量 musician。最后，这个变量的值被打印了出来。如果没有提供中间名，middle_name 将为空字符串，导致 if 测试未通过，进而执行 else 代码块：只使用名和姓来生成姓名，并将设置好格式的姓名返回函数调用行。在函数调用行，将返回的值赋给变量 musician。最后，这个变量的值被打印了出来。\n", "\n", "在调用这个函数时，如果只想指定名和姓，调用起来将非常简单。如果还要指定中间名，就必须确保它是最后一个实参，这样 Python 才能正确地将位置实参关联到形参。\n", "\n", "这个修改后的版本不仅适用于只有名和姓的人，也适用于还有中间名的人：\n", "\n", "```\n", "<PERSON><PERSON>\n", "<PERSON>\n", "```\n", "\n", "可选值在让函数能够处理各种不同情形的同时，确保函数调用尽可能简单。\n", "\n", "### 返回字典\n", "\n", "函数可返回任何类型的值，包括列表和字典等较为复杂的数据结构。例如，下面的函数接受姓名的组成部分，并返回一个表示人的字典：\n", "\n", "#### person.py 示例\n", "\n", "```python\n", "def build_person(first_name, last_name):\n", "    \"\"\" 返回一个字典，其中包含有关一个人的信息 \"\"\"\n", "    person = {'first': first_name, 'last': last_name}\n", "    return person\n", "\n", "musician = build_person('jimi', 'hendrix')\n", "print(musician)\n", "```\n", "\n", "build_person()函数接受名和姓，并将这些值放在字典中。\n", "字符数量: 704\n", "================================================================================\n", "节点 16:\n", "'hendrix')\n", "print(musician)\n", "```\n", "\n", "build_person()函数接受名和姓，并将这些值放在字典中。在存储 first_name 的值时，使用的键为'first'，而在存储 last_name 的值时，使用的键为'last'。然后，返回表示人的整个字典。在此处，打印这个被返回的值。此时，原来的两项文本信息存储在一个字典中：\n", "\n", "```\n", "{'first': 'jimi', 'last': 'hendrix'}\n", "```\n", "\n", "#### 扩展字典功能\n", "\n", "这个函数接受简单的文本信息，并将其放在一个更合适的数据结构中，让你不仅能打印这些信息，还能以其他方式处理它们。当前，字符串'jimi'和'hendrix'分别被标记为名和姓。你可以轻松地扩展这个函数，使其接受可选值，如中间名、年龄、职业或其他任何要存储的信息。例如，下面的修改能让你存储年龄：\n", "\n", "```python\n", "def build_person(first_name, last_name, age=None):\n", "    \"\"\" 返回一个字典，其中包含有关一个人的信息 \"\"\"\n", "    person = {'first': first_name, 'last': last_name}\n", "    if age:\n", "        person['age'] = age\n", "    return person\n", "```\n", "\n", "在函数定义中，新增了一个可选形参 age，其默认值被设置为特殊值 None（表示变量没有值）。可将 None 视为占位值。在条件测试中，None 相当于 False。如果函数调用中包含形参 age 的值，这个值将被存储到字典中。\n", "字符数量: 710\n", "================================================================================\n", "节点 17:\n", "在条件测试中，None 相当于 False。如果函数调用中包含形参 age 的值，这个值将被存储到字典中。在任何情况下，这个函数都会存储一个人的姓名，并且可以修改它，使其同时存储有关这个人的其他信息。\n", "\n", "### 结合使用函数和 while 循环\n", "\n", "可将函数与本书前面介绍的所有 Python 结构结合起来使用。例如，下面将结合使用 get_formatted_name()函数和 while 循环，以更正规的方式问候用户。下面尝试使用名和姓跟用户打招呼：\n", "\n", "#### greeter.py 示例\n", "\n", "```python\n", "def get_formatted_name(first_name, last_name):\n", "    \"\"\" 返回规范格式的姓名 \"\"\"\n", "    full_name = f\"{first_name} {last_name}\"\n", "    return full_name.title()\n", "\n", "# 这是一个无限循环!\n", "while True:\n", "    print(\"\\nPlease tell me your name:\")\n", "    f_name = input(\"First name: \")\n", "    l_name = input(\"Last name: \")\n", "\n", "    formatted_name = get_formatted_name(f_name, l_name)\n", "    print(f\"\\nHello, {formatted_name}!\")\n", "字符数量: 633\n", "================================================================================\n", "节点 18:\n", "```\n", "\n", "在这个示例中，使用的是 get_formatted_name()的简单版本，不涉及中间名。while 循环让用户输入姓名：提示用户依次输入名和姓。\n", "\n", "#### 提供退出条件\n", "\n", "但这个 while 循环存在一个问题：没有定义退出条件。在请用户进行一系列输入时，该在什么地方提供退出途径呢？我们要让用户能够尽可能容易地退出，因此在每次提示用户输入时，都应提供退出途径。使用 break 语句可以在每次提示用户输入时提供退出循环的简单途径：\n", "\n", "```python\n", "def get_formatted_name(first_name, last_name):\n", "    \"\"\" 返回规范格式的姓名 \"\"\"\n", "    full_name = f\"{first_name} {last_name}\"\n", "    return full_name.title()\n", "\n", "while True:\n", "    print(\"\\nPlease tell me your name:\")\n", "    print(\"(enter 'q' at any time to quit)\")\n", "\n", "    f_name = input(\"First name: \")\n", "    if f_name == 'q':\n", "        break\n", "\n", "    l_name = input(\"Last name: \")\n", "    if l_name == 'q':\n", "        break\n", "\n", "    formatted_name = get_formatted_name(f_name, l_name)\n", "    print(f\"\\nHello, {formatted_name}!\")\n", "```\n", "\n", "我们添加了一条消息来告诉用户如何退出。然后在每次提示用户输入时，都检查他输入的是否是退出值。如果是，就退出循环。现在，这个程序将不断地发出问候，直到用户输入的姓或名为'q'：\n", "\n", "```\n", "Please tell me your name:\n", "(enter 'q' at any time to quit)\n", "First name: er<PERSON>\n", "Last name: matthes\n", "\n", "Hello, <PERSON>!\n", "字符数量: 929\n", "================================================================================\n", "节点 19:\n", "Please tell me your name:\n", "(enter 'q' at any time to quit)\n", "First name: q\n", "```\n", "\n", "## 传递列表\n", "\n", "你经常会发现，向函数传递列表很有用，可能是名字列表、数值列表或更复杂的对象列表（如字典）。将列表传递给函数后，函数就能直接访问其内容。下面使用函数来提高处理列表的效率。\n", "\n", "假设有一个用户列表，而我们要向其中的每个用户发出问候。下面的示例将一个名字列表传递给一个名为 greet_users()的函数，这个函数会向列表中的每个人发出问候：\n", "\n", "#### greet_users.py 示例\n", "\n", "```python\n", "def greet_users(names):\n", "    \"\"\" 向列表中的每个用户发出简单的问候 \"\"\"\n", "    for name in names:\n", "        msg = f\"Hello, {name.title()}!\"\n", "        print(msg)\n", "\n", "usernames = ['hannah', 'ty', 'margot']\n", "greet_users(usernames)\n", "```\n", "\n", "我们将 greet_users()定义成接受一个名字列表，并将其赋给形参 names。这个函数遍历收到的列表，并对其中的每个用户打印一条问候语。在函数外，先定义一个用户列表 usernames，再调用 greet_users()并将这个列表传递给它：\n", "\n", "```\n", "Hello, <PERSON>!\n", "Hello, <PERSON>!\n", "Hello, <PERSON>got!\n", "```\n", "\n", "输出完全符合预期。每个用户都看到了一条个性化的问候语。每当需要问候一组用户时，都可调用这个函数。\n", "\n", "### 在函数中修改列表\n", "\n", "将列表传递给函数后，函数就可以对其进行修改了。在函数中对这个列表所做的任何修改都是永久的，这让你能够高效地处理大量数据。\n", "字符数量: 794\n", "================================================================================\n", "节点 20:\n", "在函数中对这个列表所做的任何修改都是永久的，这让你能够高效地处理大量数据。\n", "\n", "来看一家为用户提交的设计制作 3D 打印模型的公司。需要打印的设计事先存储在一个列表中，打印后将被移到另一个列表中。下面是在不使用函数的情况下模拟这个过程的代码：\n", "\n", "#### printing_models.py 示例\n", "\n", "```python\n", "# 首先创建一个列表，其中包含一些要打印的设计\n", "unprinted_designs = ['phone case', 'robot pendant', 'dodecahedron']\n", "completed_models = []\n", "\n", "# 模拟打印每个设计，直到没有未打印的设计为止\n", "# 打印每个设计后，都将其移到列表completed_models中\n", "while unprinted_designs:\n", "    current_design = unprinted_designs.pop()\n", "    print(f\"Printing model: {current_design}\")\n", "    completed_models.append(current_design)\n", "\n", "# 显示打印好的所有模型\n", "print(\"\\nThe following models have been printed:\")\n", "for completed_model in completed_models:\n", "    print(completed_model)\n", "```\n", "\n", "这个程序首先创建一个需要打印的设计列表，以及一个名为 completed_models 的空列表，打印每个设计后都将其移到这个空列表中。只要列表 unprinted_designs 中还有设计，while 循环就模拟打印设计的过程：从该列表末尾删除一个设计，将其赋给变量 current_design，并显示一条消息，指出正在打印当前的设计，再将该设计加入列表 completed_models。\n", "字符数量: 831\n", "================================================================================\n", "节点 21:\n", "循环结束后，显示已打印的所有设计：\n", "\n", "```\n", "Printing model: dodecahedron\n", "Printing model: robot pendant\n", "Printing model: phone case\n", "\n", "The following models have been printed:\n", "dodecahedron\n", "robot pendant\n", "phone case\n", "```\n", "\n", "#### 使用函数重新组织代码\n", "\n", "可以重新组织这些代码，编写两个函数，让每个都做一件具体的工作。大部分代码与原来相同，只是结构更为合理。第一个函数负责处理打印设计的工作，第二个概述打印了哪些设计：\n", "\n", "```python\n", "def print_models(unprinted_designs, completed_models):\n", "    \"\"\"\n", "    模拟打印每个设计，直到没有未打印的设计为止\n", "    打印每个设计后，都将其移到列表completed_models中\n", "    \"\"\"\n", "    while unprinted_designs:\n", "        current_design = unprinted_designs.pop()\n", "        print(f\"Printing model: {current_design}\")\n", "        completed_models.append(current_design)\n", "\n", "def show_completed_models(completed_models):\n", "    \"\"\" 显示打印好的所有模型 \"\"\"\n", "    print(\"\\nThe following models have been printed:\")\n", "    for completed_model in completed_models:\n", "        print(completed_model)\n", "\n", "unprinted_designs = ['phone case', 'robot pendant', 'dodecahedron']\n", "completed_models = []\n", "\n", "print_models(unprinted_designs, completed_models)\n", "show_completed_models(completed_models)\n", "```\n", "\n", "首先，定义函数 print_models()，它包含两个形参：一个需要打印的设计列表和一个打印好的模型列表。给定这两个列表，这个函数模拟打印每个设计的过程：将设计逐个从未打印的设计列表中取出，并加入打印好的模型列表。然后，定义函数 show_completed_models()，它包含一个形参：打印好的模型列表。\n", "字符数量: 1158\n", "================================================================================\n", "节点 22:\n", "然后，定义函数 show_completed_models()，它包含一个形参：打印好的模型列表。给定这个列表，函数 show_completed_models()显示打印出来的每个模型的名称。\n", "\n", "虽然这个程序的输出与未使用函数的版本相同，但是代码更有条理。完成大部分工作的代码被移到了两个函数中，让主程序很容易理解。只要看看主程序，你就能轻松地知道这个程序的功能：\n", "\n", "```python\n", "unprinted_designs = ['phone case', 'robot pendant', 'dodecahedron']\n", "completed_models = []\n", "\n", "print_models(unprinted_designs, completed_models)\n", "show_completed_models(completed_models)\n", "```\n", "\n", "我们创建了一个未打印的设计列表，以及一个空列表，后者用于存储打印好的模型。接下来，由于已经定义了两个函数，因此只需要调用它们并传入正确的实参即可。我们调用 print_models()并向它传递两个列表。像预期的一样，print_models()模拟了打印设计的过程。接下来，调用 show_completed_models()，并将打印好的模型列表传递给它，让它能够指出打印了哪些模型。描述性的函数名让阅读这些代码的人也能一目了然，虽然其中没有任何注释。\n", "\n", "相比于没有使用函数的版本，这个程序更容易扩展和维护。如果以后需要打印其他设计，只需再次调用 print_models()即可。如果发现需要对模拟打印的代码进行修改，只需修改这些代码一次，就将影响所有调用该函数的地方。\n", "字符数量: 719\n", "================================================================================\n", "节点 23:\n", "如果发现需要对模拟打印的代码进行修改，只需修改这些代码一次，就将影响所有调用该函数的地方。与必须分别修改程序的多个地方相比，这种修改的效率更高。\n", "\n", "这个程序还演示了一种理念：每个函数都应只负责一项具体工作。用第一个函数打印每个设计，用第二个函数显示打印好的模型，优于使用一个函数完成这两项工作。在编写函数时，如果发现它执行的任务太多，请尝试将这些代码划分到两个函数中。别忘了，总是可以在一个函数中调用另一个函数，这有助于将复杂的任务分解成一系列步骤。\n", "\n", "### 禁止函数修改列表\n", "\n", "有时候，需要禁止函数修改列表。假设像前一个示例那样，你有一个未打印的设计列表，并且编写了一个将这些设计移到打印好的模型列表中的函数。你可能会做出这样的决定：即便打印了所有的设计，也要保留原来的未打印的设计列表，作为存档。但由于你将所有的设计都移出了 unprinted_designs，这个列表变成了空的——原来的列表没有了。为了解决这个问题，可向函数传递列表的副本而不是原始列表。这样，函数所做的任何修改都只影响副本，而丝毫不影响原始列表。\n", "字符数量: 462\n", "================================================================================\n", "节点 24:\n", "这样，函数所做的任何修改都只影响副本，而丝毫不影响原始列表。\n", "\n", "#### 传递列表副本的方法\n", "\n", "要将列表的副本传递给函数，可以像下面这样做：\n", "\n", "```python\n", "function_name(list_name[:])\n", "```\n", "\n", "切片表示法[:]创建列表的副本。在 printing_models.py 中，如果不想清空未打印的设计列表，可像下面这样调用 print_models()：\n", "\n", "```python\n", "print_models(unprinted_designs[:], completed_models)\n", "```\n", "\n", "print_models()函数依然能够完成其工作，因为它获得了所有未打印的设计的名称，但它这次使用的是列表 unprinted_designs 的副本，而不是列表 unprinted_designs 本身。像以前一样，列表 completed_models 将包含打印好的模型的名称，但函数所做的修改不会影响列表 unprinted_designs。\n", "\n", "虽然向函数传递列表的副本可保留原始列表的内容，但除非有充分的理由，否则还是应该将原始列表传递给函数。这是因为，让函数使用现成的列表可避免花时间和内存创建副本，从而提高效率，在处理大型列表时尤其如此。\n", "\n", "## 传递任意数量的实参\n", "\n", "有时候，你预先不知道函数需要接受多少个实参，好在 Python 允许函数从调用语句中收集任意数量的实参。\n", "\n", "例如一个制作比萨的函数，它需要接受很多配料，但无法预先确定顾客要点多少种配料。\n", "字符数量: 649\n", "================================================================================\n", "节点 25:\n", "例如一个制作比萨的函数，它需要接受很多配料，但无法预先确定顾客要点多少种配料。下面的函数只有一个形参\\*toppings，不管调用语句提供了多少实参，这个形参都会将其收入囊中：\n", "\n", "#### pizza.py 示例\n", "\n", "```python\n", "def make_pizza(*toppings):\n", "    \"\"\" 打印顾客点的所有配料 \"\"\"\n", "    print(toppings)\n", "\n", "make_pizza('pepperoni')\n", "make_pizza('mushrooms', 'green peppers', 'extra cheese')\n", "```\n", "\n", "形参名\\*toppings 中的星号让 Python 创建一个名为 toppings 的元组，该元组包含函数收到的所有值。函数体内的函数调用 print()生成的输出证明，Python 既能处理使用一个值调用函数的情形，也能处理使用三个值调用函数的情形。它以类似的方式处理不同的调用。注意，Python 会将实参封装到一个元组中，即便函数只收到一个值也是如此：\n", "\n", "```\n", "('pepperoni',)\n", "('mushrooms', 'green peppers', 'extra cheese')\n", "```\n", "\n", "现在，可以将函数调用 print()替换为一个循环，遍历配料列表并对顾客点的比萨进行描述：\n", "\n", "```python\n", "def make_pizza(*toppings):\n", "    \"\"\" 概述要制作的比萨 \"\"\"\n", "    print(\"\\nMaking a pizza with the following toppings:\")\n", "    for topping in toppings:\n", "        print(f\"- {topping}\")\n", "\n", "make_pizza('pepperoni')\n", "make_pizza('mushrooms', 'green peppers',\n", "字符数量: 816\n", "================================================================================\n", "节点 26:\n", "'green peppers', 'extra cheese')\n", "```\n", "\n", "不管收到一个值还是三个值，这个函数都能妥善地处理：\n", "\n", "```\n", "Making a pizza with the following toppings:\n", "- pepperoni\n", "\n", "Making a pizza with the following toppings:\n", "- mushrooms\n", "- green peppers\n", "- extra cheese\n", "```\n", "\n", "不管函数收到多少个实参，这种语法都管用。\n", "\n", "### 结合使用位置实参和任意数量的实参\n", "\n", "如果要让函数接受不同类型的实参，必须在函数定义中将接纳任意数量实参的形参放在最后。Python 先匹配位置实参和关键字实参，再将余下的实参都收集到最后一个形参中。\n", "\n", "例如，如果前面的函数还需要一个表示比萨尺寸的形参，必须将该形参放在形参\\*toppings 的前面：\n", "\n", "```python\n", "def make_pizza(size, *toppings):\n", "    \"\"\" 概述要制作的比萨 \"\"\"\n", "    print(f\"\\nMaking a {size}-inch pizza with the following toppings:\")\n", "    for topping in toppings:\n", "        print(f\"- {topping}\")\n", "\n", "make_pizza(16, 'pepperoni')\n", "make_pizza(12, 'mushrooms', 'green peppers', 'extra cheese')\n", "```\n", "\n", "基于上述函数定义，Python 将收到的第一个值赋给形参 size，将其他所有的值都存储在元组 toppings 中。在函数调用中，首先指定表示比萨尺寸的实参，再根据需要指定任意数量的配料。\n", "字符数量: 782\n", "================================================================================\n", "节点 27:\n", "在函数调用中，首先指定表示比萨尺寸的实参，再根据需要指定任意数量的配料。\n", "\n", "现在，每个比萨都有了尺寸和一系列配料，而且这些信息被按正确的顺序打印出来了——首先是尺寸，然后是配料：\n", "\n", "```\n", "Making a 16-inch pizza with the following toppings:\n", "- pepperoni\n", "\n", "Making a 12-inch pizza with the following toppings:\n", "- mushrooms\n", "- green peppers\n", "- extra cheese\n", "```\n", "\n", "注意：你经常会看到通用形参名\\*args，它也这样收集任意数量的位置实参。\n", "\n", "### 使用任意数量的关键字实参\n", "\n", "有时候，你需要接受任意数量的实参，但预先不知道传递给函数的会是什么样的信息。在这种情况下，可将函数编写成能够接受任意数量的键值对——调用语句提供了多少就接受多少。一个这样的示例是创建用户简介：你知道将收到有关用户的信息，但不确定是什么样的信息。在下面的示例中，build_profile()函数不仅接受名和姓，还接受任意数量的关键字实参：\n", "\n", "#### user_profile.py 示例\n", "\n", "```python\n", "def build_profile(first, last, **user_info):\n", "    \"\"\" 创建一个字典，其中包含我们知道的有关用户的一切 \"\"\"\n", "    user_info['first_name'] = first\n", "    user_info['last_name'] = last\n", "    return user_info\n", "\n", "user_profile = build_profile('al<PERSON>', '<PERSON><PERSON><PERSON>',\n", "                           location='princeton',\n", "字符数量: 795\n", "================================================================================\n", "节点 28:\n", "'<PERSON><PERSON><PERSON>',\n", "                           location='princeton',\n", "                           field='physics')\n", "print(user_profile)\n", "```\n", "\n", "build_profile()函数的定义要求提供名和姓，同时允许根据需要提供任意数量的名值对。形参\\*\\*user_info 中的两个星号让 Python 创建一个名为 user_info 的字典，该字典包含函数收到的其他所有名值对。在这个函数中，可以像访问其他字典那样访问 user_info 中的名值对。\n", "\n", "在 build_profile()的函数体内，将名和姓加入字典 user_info，因为总是会从用户那里收到这两项信息，而这两项信息还没被放在字典中。接下来，将字典 user_info 返回函数调用行。\n", "\n", "我们调用 build_profile()，向它传递名（'albert'）、姓（'einstein'）和两个键值对（location='princeton'和 field='physics'），并将返回的 user_info 赋给变量 user_profile，再打印这个变量：\n", "\n", "```\n", "{'location': 'princeton', 'field': 'physics', 'first_name': 'albert', 'last_name': '<PERSON><PERSON><PERSON>'}\n", "```\n", "\n", "在这里，返回的字典包含用户的名和姓，还有居住地和研究领域。在调用这个函数时，不管额外提供多少个键值对，它都能正确地处理。\n", "\n", "在编写函数时，可以用各种方式混合使用位置实参、关键字实参和任意数量的实参。知道这些实参类型大有裨益，因为你在阅读别人编写的代码时经常会见到它们。\n", "字符数量: 767\n", "================================================================================\n", "节点 29:\n", "知道这些实参类型大有裨益，因为你在阅读别人编写的代码时经常会见到它们。要正确地使用这些类型的实参并知道使用它们的时机，需要一定的练习。就目前而言，牢记使用最简单的方法来完成任务就好了。继续往下阅读，你就会知道在各种情况下使用哪种方法的效率最高。\n", "\n", "注意：你经常会看到形参名\\*\\*kwargs，它用于收集任意数量的关键字实参。\n", "\n", "## 将函数存储在模块中\n", "\n", "使用函数的优点之一是可将代码块与主程序分离。通过给函数指定描述性名称，能让程序容易理解得多。你还可以更进一步，将函数存储在称为**模块**的独立文件中，再将模块导入（import）主程序。import 语句可让你在当前运行的程序文件中使用模块中的代码。\n", "\n", "通过将函数存储在独立的文件中，可隐藏程序代码的细节，将重点放在程序的高层逻辑上。这还能让你在众多不同的程序中复用函数。将函数存储在独立文件中后，可与其他程序员共享这些文件而不是整个程序。知道如何导入函数还能让你使用其他程序员编写的函数库。\n", "\n", "导入模块的方法有好几种，下面对每种都做简要的介绍。\n", "\n", "### 导入整个模块\n", "\n", "要让函数是可导入的，得先创建模块。**模块**是扩展名为.\n", "字符数量: 496\n", "================================================================================\n", "节点 30:\n", "### 导入整个模块\n", "\n", "要让函数是可导入的，得先创建模块。**模块**是扩展名为.py 的文件，包含要导入程序的代码。下面来创建一个包含 make_pizza()函数的模块。为此，将文件 pizza.py 中除了函数 make_pizza()之外的代码删除：\n", "\n", "#### pizza.py 模块\n", "\n", "```python\n", "def make_pizza(size, *toppings):\n", "    \"\"\" 概述要制作的比萨 \"\"\"\n", "    print(f\"\\nMaking a {size}-inch pizza with the following toppings:\")\n", "    for topping in toppings:\n", "        print(f\"- {topping}\")\n", "```\n", "\n", "接下来，在 pizza.py 所在的目录中创建一个名为 making_pizzas.py 的文件。这个文件先导入刚创建的模块，再调用 make_pizza()两次：\n", "\n", "#### making_pizzas.py 示例\n", "\n", "```python\n", "import pizza\n", "\n", "pizza.make_pizza(16, 'pepperoni')\n", "pizza.make_pizza(12, 'mushrooms', 'green peppers', 'extra cheese')\n", "```\n", "\n", "当 Python 读取这个文件时，代码行 import pizza 会让 Python 打开文件 pizza.py，并将其中的所有函数都复制到这个程序中。你看不到复制代码的过程，因为 Python 会在程序即将运行时在幕后复制这些代码。你只需要知道，在 making_pizzas.py 中，可使用 pizza.py 中定义的所有函数。\n", "\n", "要调用被导入模块中的函数，可指定被导入模块的名称 pizza 和函数名 make_pizza()，并用句点隔开。\n", "字符数量: 815\n", "================================================================================\n", "节点 31:\n", "要调用被导入模块中的函数，可指定被导入模块的名称 pizza 和函数名 make_pizza()，并用句点隔开。这些代码的输出与没有导入模块的原始程序相同：\n", "\n", "```\n", "Making a 16-inch pizza with the following toppings:\n", "- pepperoni\n", "\n", "Making a 12-inch pizza with the following toppings:\n", "- mushrooms\n", "- green peppers\n", "- extra cheese\n", "```\n", "\n", "这就是一种导入方法：只需编写一条 import 语句并在其中指定模块名，就可在程序中使用该模块中的所有函数。如果使用这种 import 语句导入了名为 module_name.py 的整个模块，就可使用下面的语法来使用其中的任意一个函数：\n", "\n", "```python\n", "module_name.function_name()\n", "```\n", "\n", "### 导入特定的函数\n", "\n", "还可以只导入模块中的特定函数，语法如下：\n", "\n", "```python\n", "from module_name import function_name\n", "```\n", "\n", "用逗号分隔函数名，可根据需要从模块中导入任意数量的函数：\n", "\n", "```python\n", "from module_name import function_0, function_1, function_2\n", "```\n", "\n", "对于前面的 making_pizzas.py 示例，如果只想导入要使用的函数，代码将类似于下面这样：\n", "\n", "```python\n", "from pizza import make_pizza\n", "\n", "make_pizza(16, 'pepperoni')\n", "make_pizza(12, 'mushrooms', 'green peppers', 'extra cheese')\n", "```\n", "\n", "如果使用这种语法，在调用函数时则无须使用句点。由于在 import 语句中显式地导入了 make_pizza()函数，因此在调用时只需指定其名称即可。\n", "字符数量: 866\n", "================================================================================\n", "节点 32:\n", "由于在 import 语句中显式地导入了 make_pizza()函数，因此在调用时只需指定其名称即可。\n", "\n", "### 使用 as 给函数指定别名\n", "\n", "如果要导入的函数的名称太长或者可能与程序中既有的名称冲突，可指定简短而独一无二的**别名**（alias）：函数的另一个名称，类似于外号。要给函数指定这种特殊的外号，需要在导入时这样做。\n", "\n", "下面给 make_pizza()函数指定了别名 mp()。这是在 import 语句中使用 make_pizza as mp 实现的，关键字 as 将函数重命名为指定的别名：\n", "\n", "```python\n", "from pizza import make_pizza as mp\n", "\n", "mp(16, 'pepperoni')\n", "mp(12, 'mushrooms', 'green peppers', 'extra cheese')\n", "```\n", "\n", "上面的 import 语句将函数 make_pizza()重命名为 mp()。在这个程序中，每当需要调用 make_pizza()时，都可将其简写成 mp()。Python 将运行 make_pizza()中的代码，同时避免与程序可能包含的 make_pizza()函数混淆。\n", "\n", "指定别名的通用语法如下：\n", "\n", "```python\n", "from module_name import function_name as fn\n", "```\n", "\n", "### 使用 as 给模块指定别名\n", "\n", "还可以给模块指定别名。通过给模块指定简短的别名（如给 pizza 模块指定别名 p），你能够更轻松地调用模块中的函数。相比于 pizza.make_pizza()，p.make_pizza()显然更加简洁：\n", "\n", "```python\n", "import pizza as p\n", "\n", "p.make_pizza(16, 'pepperoni')\n", "p.make_pizza(12,\n", "字符数量: 793\n", "================================================================================\n", "节点 33:\n", "make_pizza()，p.make_pizza()显然更加简洁：\n", "\n", "```python\n", "import pizza as p\n", "\n", "p.make_pizza(16, 'pepperoni')\n", "p.make_pizza(12, 'mushrooms', 'green peppers', 'extra cheese')\n", "```\n", "\n", "上述 import 语句给 pizza 模块指定了别名 p，但该模块中所有函数的名称都没变。要调用 make_pizza()函数，可将其写为 p.make_pizza()而不是 pizza.make_pizza()。这样不仅让代码更加简洁，还让你不用再关注模块名，只专注于描述性的函数名。这些函数名明确地指出了函数的功能，对于理解代码来说，它们比模块名更重要。\n", "\n", "给模块指定别名的通用语法如下：\n", "\n", "```python\n", "import module_name as mn\n", "```\n", "\n", "### 导入模块中的所有函数\n", "\n", "使用星号（\\*）运算符可让 Python 导入模块中的所有函数：\n", "\n", "```python\n", "from pizza import *\n", "\n", "make_pizza(16, 'pepperoni')\n", "make_pizza(12, 'mushrooms', 'green peppers', 'extra cheese')\n", "```\n", "\n", "import 语句中的星号让 Python 将模块 pizza 中的每个函数都复制到这个程序文件中。由于导入了每个函数，可通过名称来调用每个函数，无须使用**点号**（dot notation）。\n", "字符数量: 675\n", "================================================================================\n", "节点 34:\n", "由于导入了每个函数，可通过名称来调用每个函数，无须使用**点号**（dot notation）。然而，在使用并非自己编写的大型模块时，最好不要使用这种导入方法，因为如果模块中有函数的名称与当前项目中既有的名称相同，可能导致意想不到的结果：Python 可能会因为遇到多个名称相同的函数或变量而覆盖函数，而不是分别导入所有的函数。\n", "\n", "最佳的做法是，要么只导入需要使用的函数，要么导入整个模块并使用点号。这都能让代码更清晰，更容易阅读和理解。这里之所以介绍导入模块中所有函数的方法，只是想让你在阅读别人编写的代码时，能够理解类似于下面的 import 语句：\n", "\n", "```python\n", "from module_name import *\n", "```\n", "\n", "## 函数编写指南\n", "\n", "在编写函数时，需要牢记几个细节。应给函数指定描述性名称，且只使用小写字母和下划线。描述性名称可帮助你和别人明白代码想要做什么。在给模块命名时也应遵循上述约定。\n", "\n", "每个函数都应包含简要阐述其功能的注释。该注释应紧跟在函数定义后面，并采用文档字符串的格式。\n", "字符数量: 457\n", "================================================================================\n", "节点 35:\n", "每个函数都应包含简要阐述其功能的注释。该注释应紧跟在函数定义后面，并采用文档字符串的格式。这样，其他程序员只需阅读文档字符串中的描述就能够使用它：他们完全可以相信代码会如描述的那样运行，并且只要知道函数名、需要的实参以及返回值的类型，就能在自己的程序中使用它。\n", "\n", "### 格式规范\n", "\n", "在给形参指定默认值时，等号两边不要有空格：\n", "\n", "```python\n", "def function_name(parameter_0, parameter_1='default value')\n", "```\n", "\n", "函数调用中的关键字实参也应遵循这种约定：\n", "\n", "```python\n", "function_name(value_0, parameter_1='value')\n", "```\n", "\n", "PEP 8 建议代码行的长度不要超过 79 个字符。这样，只要编辑器窗口适中，就能看到整行代码。如果形参很多，导致函数定义的长度超过了 79 个字符，可在函数定义中输入左括号后按回车键，并在下一行连按两次制表符键，从而将形参列表和只缩进一层的函数体区分开来。\n", "\n", "大多数编辑器会自动对齐后续参数列表行，使其缩进程度与你给第一个参数列表行指定的缩进程度相同：\n", "\n", "```python\n", "def function_name(\n", "        parameter_0, parameter_1, parameter_2,\n", "        parameter_3, parameter_4, parameter_5):\n", "    function body...\n", "```\n", "\n", "如果程序或模块包含多个函数，可使用两个空行将相邻的函数分开。\n", "字符数量: 680\n", "================================================================================\n", "节点 36:\n", "parameter_3, parameter_4, parameter_5):\n", "    function body...\n", "```\n", "\n", "如果程序或模块包含多个函数，可使用两个空行将相邻的函数分开。这样将更容易知道前一个函数到什么地方结束，下一个函数从什么地方开始。\n", "\n", "所有的 import 语句都应放在文件开头。唯一的例外是，你要在文件开头使用注释来描述整个程序。\n", "\n", "## 小结\n", "\n", "在本章中，你首先学习了如何编写函数，以及如何传递实参，让函数能够访问完成工作所需的信息。然后学习了如何使用位置实参和关键字实参，以及如何接受任意数量的实参。你见识了显示输出的函数和返回值的函数，知道了如何将函数与列表、字典、if 语句和 while 循环结合起来使用，以及如何将函数存储在称为模块的独立文件中，让程序文件更简单、更易于理解。最后，你了解了函数编写指南，遵循这些指南可让程序始终保持良好的结构，对你和其他人来说都易于阅读。\n", "\n", "程序员的目标之一是编写简单的代码来完成任务，而函数有助于实现这样的目标。使用它们，你在编写好一个个代码块并确定其能够正确运行后，就可不必在上面花更多精力。确定函数能够正确地完成工作后，你就可以接着投身于下一个编程任务，因为你知道它们以后也不会出问题。\n", "字符数量: 534\n", "================================================================================\n", "节点 37:\n", "确定函数能够正确地完成工作后，你就可以接着投身于下一个编程任务，因为你知道它们以后也不会出问题。\n", "\n", "函数让你在编写一次代码后，可以复用它们任意多次。当需要运行函数中的代码时，只需编写一行函数调用代码，就能让函数完成其工作。当需要修改函数的行为时，只需修改一个代码块，你所做的修改就将影响调用这个函数的每个地方。\n", "\n", "使用函数让程序更容易阅读，而良好的函数名概述了程序各个部分的作用。相比于阅读一系列代码块，阅读一系列函数调用让你能够更快地明白程序的作用。\n", "\n", "函数还让代码更容易测试和调试。如果程序使用一系列函数来完成任务，其中的每个函数都完成一项具体工作，那么程序测试和维护起来将容易得多：可编写分别调用每个函数的程序，并测试每个函数是否在可能的各种情形下都能正确地运行。经过这样的测试，你就能深信每次调用这些函数时，它们都将正确地运行。\n", "\n", "在第 9 章中，你将学习编写类。类将函数和数据整洁地封装起来，让你能够灵活而高效地使用它们。\n", "字符数量: 416\n", "================================================================================\n"]}], "source": ["# 用句子级别的方法切文本块\n", "from llama_index.core.node_parser import SentenceSplitter\n", "\n", "splitter = SentenceSplitter(chunk_size=512, chunk_overlap=50)\n", "nodes = splitter.get_nodes_from_documents(documents)\n", "\n", "print(f\"总共生成了 {len(nodes)} 个节点\")\n", "print(\"=\" * 80)\n", "\n", "for i, node in enumerate(nodes):\n", "    print(f\"节点 {i+1}:\")\n", "    print(node.text)\n", "    print(f\"字符数量: {len(node.text)}\")\n", "    print(\"=\" * 80)\n"]}, {"cell_type": "code", "execution_count": 4, "id": "a50f3bfe", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--------------------------------------------------------------------------------\n", "🧱 构建向量索引 VectorStoreIndex ...\n", "✅ 索引就绪！\n", "\n", "--------------------------------------------------------------------------------\n", "\n"]}], "source": ["# =============================\n", "# 3) 构建 VectorStoreIndex\n", "# =============================\n", "print(\"\\n\" + \"-\"*80)\n", "print(\"🧱 构建向量索引 VectorStoreIndex ...\") \n", "index = VectorStoreIndex(nodes)\n", "print(\"✅ 索引就绪！\\n\")\n", "print(\"-\"*80 + \"\\n\")"]}, {"cell_type": "code", "execution_count": 5, "id": "43c7ab54", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==========================================================================================\n", "🧠 已创建 Memory（Static + FactExtraction，无向量块）\n", "session_id           : demo-python-rag\n", "token_limit          : 1000  | flush_size: 200\n", "chat_history_ratio   : 0.6\n", "blocks               : ['StaticMemoryBlock', 'FactExtractionMemoryBlock']\n", "==========================================================================================\n", "\n"]}], "source": ["\n", "# === 新版 Memory：仅使用 StaticMemoryBlock 与 FactExtractionMemoryBlock ========\n", "from llama_index.core.memory import Memory, StaticMemoryBlock, FactExtractionMemoryBlock\n", "\n", "static_block = StaticMemoryBlock(\n", "    name=\"course_policy\",\n", "    static_content=(\n", "        \"【课程背景】Python 初学者教程；\"\n", "        \"【助教规则】优先基于检索到的文档回答；无法从文档找到答案时，明确说明不知道；\"\n", "        \"回答要举【最小可运行】示例、避免过度抽象；\"\n", "        \"语言：中文；风格：清晰、简短、分点说明。\"\n", "    ),\n", ")\n", "\n", "fact_block = FactExtractionMemoryBlock(\n", "    name=\"learner_facts\",\n", "    max_facts=8,\n", ")\n", "\n", "memory = Memory.from_defaults(\n", "    session_id=\"demo-python-rag\",\n", "    memory_blocks=[static_block, fact_block],\n", "    insert_method=\"system\",\n", "    token_limit=1000,\n", "    token_flush_size=200,\n", "    chat_history_token_ratio=0.6\n", ")\n", "\n", "print(\"\\n\" + \"=\"*90)\n", "print(\"🧠 已创建 Memory（Static + FactExtraction，无向量块）\")\n", "print(\"session_id           :\", memory.session_id)\n", "print(\"token_limit          :\", memory.token_limit, \" | flush_size:\", memory.token_flush_size)\n", "print(\"chat_history_ratio   :\", memory.chat_history_token_ratio)\n", "print(\"blocks               :\", [type(b).__name__ for b in memory.memory_blocks])\n", "print(\"=\"*90 + \"\\n\")\n"]}, {"cell_type": "code", "execution_count": 6, "id": "703e4508", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==========================================================================================\n", "📜 默认 fact_extraction_prompt_template：\n", "\n", "You are a precise fact extraction system designed to identify key information from conversations.\n", "\n", "INSTRUCTIONS:\n", "1. Review the conversation segment provided prior to this message\n", "2. Extract specific, concrete facts the user has disclosed or important information discovered\n", "3. Focus on factual information like preferences, personal details, requirements, constraints, or context\n", "4. Format each fact as a separate <fact> XML tag\n", "5. Do not include opinions, summaries, or interpretations - only extract explicit information\n", "6. Do not duplicate facts that are already in the existing facts list\n", "\n", "<existing_facts>\n", "{{ existing_facts }}\n", "</existing_facts>\n", "\n", "Return ONLY the extracted facts in this exact format:\n", "<facts>\n", "  <fact>Specific fact 1</fact>\n", "  <fact>Specific fact 2</fact>\n", "  <!-- More facts as needed -->\n", "</facts>\n", "\n", "If no new facts are present, return: <facts></facts>\n", "\n", "--------------------------------------------------------------------------------\n", "📜 默认 fact_condense_prompt_template：\n", "\n", "You are a precise fact condensing system designed to identify key information from conversations.\n", "\n", "INSTRUCTIONS:\n", "1. Review the current list of existing facts\n", "2. Condense the facts into a more concise list, less than {{ max_facts }} facts\n", "3. Focus on factual information like preferences, personal details, requirements, constraints, or context\n", "4. Format each fact as a separate <fact> XML tag\n", "5. Do not include opinions, summaries, or interpretations - only extract explicit information\n", "6. Do not duplicate facts that are already in the existing facts list\n", "\n", "<existing_facts>\n", "{{ existing_facts }}\n", "</existing_facts>\n", "\n", "Return ONLY the condensed facts in this exact format:\n", "<facts>\n", "  <fact>Specific fact 1</fact>\n", "  <fact>Specific fact 2</fact>\n", "  <!-- More facts as needed -->\n", "</facts>\n", "\n", "If no new facts are present, return: <facts></facts>\n", "==========================================================================================\n", "\n"]}], "source": ["\n", "# === 读取并打印 FactExtractionMemoryBlock 的“默认提示词模板” ===================\n", "from llama_index.core.prompts import BasePromptTemplate\n", "\n", "def _get_tmpl(p):\n", "    return getattr(p, \"template_str\", str(p))\n", "\n", "print(\"\\n\" + \"=\"*90)\n", "print(\"📜 默认 fact_extraction_prompt_template：\\n\")\n", "print(_get_tmpl(fact_block.fact_extraction_prompt_template))\n", "\n", "print(\"\\n\" + \"-\"*80)\n", "print(\"📜 默认 fact_condense_prompt_template：\\n\")\n", "print(_get_tmpl(fact_block.fact_condense_prompt_template))\n", "print(\"=\"*90 + \"\\n\")\n"]}, {"cell_type": "code", "execution_count": 6, "id": "0127d1fa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==========================================================================================\n", "✨ 已替换为 *自定义* 模板：\n", "\n", "--- 自定义 fact_extraction_prompt_template ---\n", "\n", "\n", "你是对话中的“事实抽取器”。\n", "请从 **用户与助教最近的对话片段** 中，抽取【明确且可验证】的信息（例如：用户身份/偏好/目标/限制/公司/项目名/版本号等）。\n", "避免观点与推测。不要重复已存在的事实。\n", "\n", "<existing_facts>\n", "{{ existing_facts }}\n", "</existing_facts>\n", "\n", "仅用如下 XML 返回：\n", "<facts>\n", "  <fact>具体事实1</fact>\n", "  <fact>具体事实2</fact>\n", "</facts>\n", "若无新增事实，返回：<facts></facts>\n", "\n", "\n", "--- 自定义 fact_condense_prompt_template ---\n", "\n", "\n", "你是“事实压缩器”。在不丢关键信息的前提下，将现有 facts 压缩到 **少于 {{ max_facts }} 条**。\n", "去重、合并相近表述，保留对后续解题最有帮助的要点。\n", "\n", "<existing_facts>\n", "{{ existing_facts }}\n", "</existing_facts>\n", "\n", "仅用如下 XML 返回：\n", "<facts>\n", "  <fact>具体事实1</fact>\n", "  <fact>具体事实2</fact>\n", "</facts>\n", "若无需压缩，返回：<facts></facts>\n", "\n", "==========================================================================================\n", "\n"]}], "source": ["\n", "# === 自定义两段模板（更贴合“Python 教程 + 学员画像”场景） =====================\n", "from llama_index.core.prompts import RichPromptTemplate\n", "\n", "fact_block.fact_extraction_prompt_template = RichPromptTemplate(\n", "    r'''\n", "你是对话中的“事实抽取器”。\n", "请从 **用户与助教最近的对话片段** 中，抽取【明确且可验证】的信息（例如：用户身份/偏好/目标/限制/公司/项目名/版本号等）。\n", "避免观点与推测。不要重复已存在的事实。\n", "\n", "<existing_facts>\n", "{{ existing_facts }}\n", "</existing_facts>\n", "\n", "仅用如下 XML 返回：\n", "<facts>\n", "  <fact>具体事实1</fact>\n", "  <fact>具体事实2</fact>\n", "</facts>\n", "若无新增事实，返回：<facts></facts>\n", "'''\n", ")\n", "\n", "fact_block.fact_condense_prompt_template = RichPromptTemplate(\n", "    r'''\n", "你是“事实压缩器”。在不丢关键信息的前提下，将现有 facts 压缩到 **少于 {{ max_facts }} 条**。\n", "去重、合并相近表述，保留对后续解题最有帮助的要点。\n", "\n", "<existing_facts>\n", "{{ existing_facts }}\n", "</existing_facts>\n", "\n", "仅用如下 XML 返回：\n", "<facts>\n", "  <fact>具体事实1</fact>\n", "  <fact>具体事实2</fact>\n", "</facts>\n", "若无需压缩，返回：<facts></facts>\n", "'''\n", ")\n", "\n", "print(\"\\n\" + \"=\"*90)\n", "print(\"✨ 已替换为 *自定义* 模板：\")\n", "print(\"\\n--- 自定义 fact_extraction_prompt_template ---\\n\")\n", "print(getattr(fact_block.fact_extraction_prompt_template, \"template_str\", str(fact_block.fact_extraction_prompt_template)))\n", "print(\"\\n--- 自定义 fact_condense_prompt_template ---\\n\")\n", "print(getattr(fact_block.fact_condense_prompt_template, \"template_str\", str(fact_block.fact_condense_prompt_template)))\n", "print(\"=\"*90 + \"\\n\")\n"]}, {"cell_type": "code", "execution_count": 7, "id": "f166dcfd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==========================================================================================\n", "🤝 两个 chat_engine 已创建，并共享同一个 Memory\n", "condense_question -> 用于 RAG 问答\n", "simple            -> 纯模型对话，但依然会注入 Static/Facts 记忆\n", "==========================================================================================\n", "\n"]}], "source": ["\n", "# === 创建两个 chat_engine，并共享“同一个” Memory ==============================\n", "system_prompt_rag = (\n", "    \"你是Python课程助教。你将基于检索到的课程片段回答，\"\n", "    \"若没有足够证据，直接说明不知道；回答尽量短小分点，并给最小可运行示例。\"\n", ")\n", "\n", "condense_engine = index.as_chat_engine(\n", "    chat_mode=\"condense_question\",\n", "    memory=memory,\n", "    similarity_top_k=3,\n", "    verbose=True,\n", ")\n", "\n", "simple_engine = index.as_chat_engine(\n", "    chat_mode=\"simple\",\n", "    memory=memory,\n", "    system_prompt=\"你是耐心的Python助教；回答要简洁并引用你“记忆”中保存的事实。\",\n", "    verbose=True,\n", ")\n", "\n", "print(\"\\n\" + \"=\"*90)\n", "print(\"🤝 两个 chat_engine 已创建，并共享同一个 Memory\")\n", "print(\"condense_question -> 用于 RAG 问答\")\n", "print(\"simple            -> 纯模型对话，但依然会注入 Static/Facts 记忆\")\n", "print(\"=\"*90 + \"\\n\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "89c3127b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==========================================================================================\n", "🗣️ [Round 1 - RAG] 问：'Python 中如何定义函数？能给我最小示例吗？'\n", "Querying with: 在 Python 中，如何定义函数？请提供一个最小的可运行示例。\n", "\n", "🔎 condense_question 答：\n", " 在 Python 中，定义函数的基本结构如下：\n", "\n", "```python\n", "def function_name():\n", "    \"\"\" 函数的文档字符串，描述函数的功能 \"\"\"\n", "    # 函数体\n", "    print(\"Hello!\")\n", "```\n", "\n", "这是一个最小的可运行示例，定义了一个名为 `function_name` 的函数，它在被调用时会打印 \"Hello!\"。要调用这个函数，可以使用以下代码：\n", "\n", "```python\n", "function_name()\n", "```\n", "\n", "这将输出：\n", "\n", "```\n", "Hello!\n", "``` \n", "\n", "------------------------------------------------------------------------------------------\n", "🗣️ [Round 2 - 记录事实] 我补充资料：‘我的公司叫 ZenFlow，我们偏好多用代码示例进行教学。请记住。’\n", "Querying with: 请问在 Python 中如何定义函数，并能提供一个最小的代码示例吗？另外，我想强调一下，我的公司叫 ZenFlow，我们偏好多用代码示例进行教学。\n", "\n", "🧠 答：\n", " 在 Python 中，定义函数的基本结构如下：\n", "\n", "1. 使用关键字 `def` 来开始函数定义。\n", "2. 指定函数名，后面跟着括号，括号内可以包含参数（如果需要）。\n", "3. 函数定义以冒号结尾。\n", "4. 紧接着是函数体，包含实际执行的代码。\n", "5. 可以在函数定义后添加文档字符串，用于描述函数的功能。\n", "\n", "以下是一个最小的代码示例，展示了如何定义一个简单的函数：\n", "\n", "```python\n", "def greet_user():\n", "    \"\"\" 显示简单的问候语 \"\"\"\n", "    print(\"Hello!\")\n", "\n", "greet_user()\n", "```\n", "\n", "在这个示例中，`greet_user` 函数没有参数，调用它时会打印出 \"Hello!\"。 \n", "\n", "Querying with: 请问在 Python 中如何定义函数，并能提供一个最小可运行的示例吗？请使用分点说明的方式。\n", "------------------------------------------------------------------------------------------\n", "🧾 当前已抽取的 facts（来自 FactExtractionMemoryBlock）：\n", "['用户的公司叫 ZenFlow', '用户偏好多用代码示例进行教学']\n", "------------------------------------------------------------------------------------------\n", "🗣️ [Round 3 - 无检索，仅用记忆] 问 simple_engine：‘我公司的名字是什么？结合我的偏好，总结上面函数要点。’\n", "\n", "💬 simple 答：\n", " 你的公司名字是 ZenFlow。以下是关于定义函数的要点总结，结合你的偏好：\n", "\n", "1. **定义函数**：\n", "   - 使用 `def` 关键字。\n", "   - 后接函数名和参数列表。\n", "\n", "2. **参数**：\n", "   - 可以定义参数，使用逗号分隔。\n", "   - 可为参数指定默认值。\n", "\n", "3. **文档字符串**：\n", "   - 在函数体第一行添加描述性文档字符串。\n", "\n", "4. **函数体**：\n", "   - 编写具体的实现代码。\n", "\n", "5. **调用函数**：\n", "   - 使用函数名来执行函数。\n", "\n", "### 示例代码：\n", "\n", "```python\n", "def greet_user():\n", "    \"\"\" 显示简单的问候语 \"\"\"\n", "    print(\"Hello!\")\n", "\n", "# 调用函数\n", "greet_user()\n", "```\n", "\n", "这个示例展示了如何定义和调用一个简单的函数。 \n", "\n", "==========================================================================================\n", "\n"]}], "source": ["\n", "# === 多轮问答（RAG + 共享记忆） ================================================\n", "from llama_index.core.llms import ChatMessage, MessageRole\n", "\n", "print(\"\\n\" + \"=\"*90)\n", "print(\"🗣️ [Round 1 - RAG] 问：'Python 中如何定义函数？能给我最小示例吗？'\")\n", "resp1 = condense_engine.chat(\"Python 中如何定义函数？能给我最小示例吗？\")\n", "print(\"\\n🔎 condense_question 答：\\n\", resp1, \"\\n\")\n", "\n", "print(\"-\"*90)\n", "print(\"🗣️ [Round 2 - 记录事实] 我补充资料：‘我的公司叫 ZenFlow，我们偏好多用代码示例进行教学。请记住。’\")\n", "resp2 = condense_engine.chat(\"我的公司叫 ZenFlow，我们偏好多用代码示例进行教学。请记住。\")\n", "print(\"\\n🧠 答：\\n\", resp2, \"\\n\")\n", "\n", "condense_engine.chat(\"再次提醒：偏好『最小可运行』示例与分点说明。\")\n", "\n", "print(\"-\"*90)\n", "print(\"🧾 当前已抽取的 facts（来自 FactExtractionMemoryBlock）：\")\n", "print(getattr(fact_block, \"facts\", []))\n", "\n", "print(\"-\"*90)\n", "print(\"🗣️ [Round 3 - 无检索，仅用记忆] 问 simple_engine：‘我公司的名字是什么？结合我的偏好，总结上面函数要点。’\")\n", "resp3 = simple_engine.chat(\"我公司的名字是什么？结合我的偏好，总结上面函数要点。\")\n", "print(\"\\n💬 simple 答：\\n\", resp3, \"\\n\")\n", "\n", "print(\"=\"*90 + \"\\n\")\n"]}, {"cell_type": "markdown", "id": "b1e75d51", "metadata": {}, "source": ["# 完整观测 memory的行为"]}, {"cell_type": "code", "execution_count": 19, "id": "a11570a6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==========================================================================================\n", "🗣️ [Round 1 - RAG] \n", "Querying with: 在Python中，参数的概念是什么？为什么我觉得它很难理解？\n", "\n", "🔎 condense_question 答：\n", " 在Python中，参数的概念主要包括形参和实参。形参是函数定义中用于接收信息的变量，而实参是在调用函数时传递给函数的具体值。理解这两个概念的区别是关键。\n", "\n", "形参是函数所需的信息的占位符，而实参则是实际传递给函数的值。由于函数可以有多个形参，因此在调用时需要确保提供的实参数量和顺序与形参一致，这可能导致一些混淆。此外，使用位置实参、关键字实参和默认值等多种传递方式也可能让人感到复杂。\n", "\n", "如果在调用函数时实参与形参不匹配，可能会出现错误，这进一步增加了理解的难度。掌握这些概念和规则需要一定的时间和实践。 \n", "\n", "\n", "🧠 Memory 观测 - Round 1:\n", "—— Memory.get() (最终传给LLM的消息) ——\n", "[0] MessageRole.SYSTEM: <memory>\n", "<course_policy>\n", "【课程背景】Python 初学者教程；【助教规则】优先基于检索到的文档回答；无法从文档找到答案时，明确说明不知道；回答要举【最小可运行】示例、避免过度抽象；语言：中文；风格：清晰、简短、分点说明。\n", "</course_policy>\n", "<learner_facts>\n", "<fact>用户的公司叫 ZenFlow</fact>\n", "<fact>用户偏好多用代码示例进行教学</fact>\n", "</learner_facts>\n", "</memory>\n", "[1] MessageRole.USER: 说说没钱的时候应该怎么办？\n", "[2] MessageRole.ASSISTANT: 虽然我主要是帮助你学习Python，但我可以给你一些建议，帮助你在经济紧张时应对：\n", "\n", "1. **制定预算**：记录所有收入和支出，找出可以削减的开支。\n", "   \n", "2. **优先支付必要费用**：确保支付基本生活费用，如房租、水电费和食物。\n", "\n", "3. **寻找额外收入**：考虑兼职工作或自由职业，利用自己的技能赚取额外收入。\n", "\n", "4. **利用社区资源**：许多地方有食品银行、社区援助项目等，可以提供帮助。\n", "\n", "5. **学习理财知识**：通过书籍或在线课程提升自己的财务管理能力。\n", "\n", "6. **与朋友和家人沟通**：在需要时寻求支持，可能会有意想不到的帮助。\n", "\n", "希望这些建议能对你有所帮助！如果你有关于Python的问题，随时问我！\n", "[3] MessageRole.USER: 如何理解python中所说的参数，感觉是个很难理解的概念呢！\n", "[4] MessageRole.ASSISTANT: 在Python中，参数的概念主要包括形参和实参。形参是函数定义中用于接收信息的变量，而实参是在调用函数时传递给函数的具体值。理解这两个概念的区别是关键。\n", "\n", "形参是函数所需的信息的占位符，而实参则是实际传递给函数的值。由于函数可以有多个形参，因此在调用时需要确保提供的实参数量和顺序与形参一致，这可能导致一些混淆。此外，使用位置实参、关键字实参和默认值等多种传递方式也可能让人感到复杂。\n", "\n", "如果在调用函数时实参与形参不匹配，可能会出现错误，这进一步增加了理解的难度。掌握这些概念和规则需要一定的时间和实践。\n", "\n", "—— Memory Blocks 详情 ——\n", "[1] course_policy <StaticMemoryBlock>\n", "  • static_content: [TextBlock(block_type='text', text='【课程背景】Python 初学者教程；【助教规则】优先基于检索到的文档回答；无法从文档找到答案时，明确说明不知道；回答要举【最小可运行】示例、避免过度抽象；语言：中文；风格：清晰、简短、分点说明。')]\n", "[2] learner_facts <FactExtractionMemoryBlock>\n", "  • facts 数量: 2\n", "    [1] 用户的公司叫 ZenFlow\n", "    [2] 用户偏好多用代码示例进行教学\n", "\n", "—— Memory 统计信息 ——\n", "session_id: demo-python-rag\n", "token_limit: 1000\n", "token_flush_size: 200\n", "chat_history_token_ratio: 0.6\n", "------------------------------------------------------------------------------------------\n", "🗣️ [Round 2 - 记录事实] \n", "Querying with: 在Python中，如何理解参数的概念，特别是形参和实参之间的区别，以及在函数调用时如何正确传递这些参数？\n", "\n", "🧠 答：\n", " 在Python中，参数的概念可以通过形参和实参来理解。形参是函数定义中用于接收信息的变量，而实参是在调用函数时实际传递给函数的值。形参在函数定义时指定，实参则是在函数调用时提供。\n", "\n", "在函数调用时，实参可以通过多种方式传递给形参。最基本的方式是使用位置实参，这要求实参的顺序与形参的顺序一致。此外，还可以使用关键字实参，其中每个实参由变量名和值组成，或者使用列表和字典来传递参数。\n", "\n", "在使用函数时，确保实参的数量与形参匹配是非常重要的。如果提供的实参数量不正确，Python会抛出实参不匹配的错误，提示缺少必要的实参或多余的实参。这种错误信息通常会指明问题所在，帮助开发者快速定位并修正错误。\n", "\n", "通过理解形参和实参的区别，以及正确传递参数的方法，可以有效地使用函数并避免常见的错误。 \n", "\n", "\n", "🧠 Memory 观测 - Round 2:\n", "—— Memory.get() (最终传给LLM的消息) ——\n", "[0] MessageRole.SYSTEM: <memory>\n", "<course_policy>\n", "【课程背景】Python 初学者教程；【助教规则】优先基于检索到的文档回答；无法从文档找到答案时，明确说明不知道；回答要举【最小可运行】示例、避免过度抽象；语言：中文；风格：清晰、简短、分点说明。\n", "</course_policy>\n", "<learner_facts>\n", "<fact>用户的公司叫 ZenFlow</fact>\n", "<fact>用户偏好多用代码示例进行教学</fact>\n", "</learner_facts>\n", "</memory>\n", "[1] MessageRole.USER: 我对html、css、js了解比较多，还有，我的目标是拯救全世界，你要记住。\n", "[2] MessageRole.ASSISTANT: 在Python中，参数的概念可以通过形参和实参来理解。形参是函数定义中用于接收信息的变量，而实参是在调用函数时实际传递给函数的值。形参在函数定义时指定，实参则是在函数调用时提供。\n", "\n", "在函数调用时，实参可以通过多种方式传递给形参。最基本的方式是使用位置实参，这要求实参的顺序与形参的顺序一致。此外，还可以使用关键字实参，其中每个实参由变量名和值组成，或者使用列表和字典来传递参数。\n", "\n", "在使用函数时，确保实参的数量与形参匹配是非常重要的。如果提供的实参数量不正确，Python会抛出实参不匹配的错误，提示缺少必要的实参或多余的实参。这种错误信息通常会指明问题所在，帮助开发者快速定位并修正错误。\n", "\n", "通过理解形参和实参的区别，以及正确传递参数的方法，可以有效地使用函数并避免常见的错误。\n", "\n", "—— Memory Blocks 详情 ——\n", "[1] course_policy <StaticMemoryBlock>\n", "  • static_content: [TextBlock(block_type='text', text='【课程背景】Python 初学者教程；【助教规则】优先基于检索到的文档回答；无法从文档找到答案时，明确说明不知道；回答要举【最小可运行】示例、避免过度抽象；语言：中文；风格：清晰、简短、分点说明。')]\n", "[2] learner_facts <FactExtractionMemoryBlock>\n", "  • facts 数量: 2\n", "    [1] 用户的公司叫 ZenFlow\n", "    [2] 用户偏好多用代码示例进行教学\n", "\n", "—— Memory 统计信息 ——\n", "session_id: demo-python-rag\n", "token_limit: 1000\n", "token_flush_size: 200\n", "chat_history_token_ratio: 0.6\n", "------------------------------------------------------------------------------------------\n", "🗣️ [Round 2.5 - 追加事实] \n", "Querying with: 你能用理查德·费曼的风格来讲解Python中的参数（形参和实参）吗？\n", "\n", "🧠 Memory 观测 - Round 2.5:\n", "—— Memory.get() (最终传给LLM的消息) ——\n", "[0] MessageRole.SYSTEM: <memory>\n", "<course_policy>\n", "【课程背景】Python 初学者教程；【助教规则】优先基于检索到的文档回答；无法从文档找到答案时，明确说明不知道；回答要举【最小可运行】示例、避免过度抽象；语言：中文；风格：清晰、简短、分点说明。\n", "</course_policy>\n", "<learner_facts>\n", "<fact>用户的公司叫 ZenFlow</fact>\n", "<fact>用户偏好多用代码示例进行教学</fact>\n", "<fact>用户对HTML、CSS、JS了解比较多</fact>\n", "<fact>用户的目标是拯救全世界</fact>\n", "</learner_facts>\n", "</memory>\n", "[1] MessageRole.ASSISTANT: 想象一下，你在举办一个派对，邀请朋友们来。你需要告诉他们你希望他们带什么食物。这里的“食物”就像是函数的参数。\n", "\n", "首先，形参就像是你在邀请函上写的食物清单。比如，你写道：“请带一个沙拉。”这个“沙拉”就是形参，它告诉你的朋友们你希望他们带什么，但并没有具体说明是谁带的。\n", "\n", "接下来，实参就像是你朋友们实际带来的食物。当你的朋友杰西带来了一个美味的凯撒沙拉时，这个凯撒沙拉就是实参。它是你在派对上实际收到的东西，正好对应你邀请函上的“沙拉”。\n", "\n", "在Python中，函数的定义就像是你的邀请函，里面列出了形参。而当你调用这个函数时，就像是你的朋友们回应了邀请，带来了实参。你可以有多个形参，就像你可以在邀请函上要求朋友们带不同的食物一样。\n", "\n", "有时候，你可能会希望朋友们带不同数量的食物，这时你可以在邀请函上写“请带任意数量的食物”。在Python中，这就对应于使用可变参数，允许你在函数中接收任意数量的实参。\n", "\n", "最后，函数不仅仅是收集食物，它们还可以把食物组合在一起，做成一顿丰盛的晚餐。这个过程就像是函数返回值的概念。你可以把所有的食物混合在一起，得到一顿美味的晚餐，这个晚餐就是函数的返回值。\n", "\n", "所以，形参和实参就像是派对邀请和朋友们带来的食物，理解它们的关系可以帮助你更好地掌握Python中的函数。\n", "\n", "—— Memory Blocks 详情 ——\n", "[1] course_policy <StaticMemoryBlock>\n", "  • static_content: [TextBlock(block_type='text', text='【课程背景】Python 初学者教程；【助教规则】优先基于检索到的文档回答；无法从文档找到答案时，明确说明不知道；回答要举【最小可运行】示例、避免过度抽象；语言：中文；风格：清晰、简短、分点说明。')]\n", "[2] learner_facts <FactExtractionMemoryBlock>\n", "  • facts 数量: 4\n", "    [1] 用户的公司叫 ZenFlow\n", "    [2] 用户偏好多用代码示例进行教学\n", "    [3] 用户对HTML、CSS、JS了解比较多\n", "    [4] 用户的目标是拯救全世界\n", "\n", "—— Memory 统计信息 ——\n", "session_id: demo-python-rag\n", "token_limit: 1000\n", "token_flush_size: 200\n", "chat_history_token_ratio: 0.6\n", "------------------------------------------------------------------------------------------\n", "🗣️ [Round 3 - 无检索，仅用记忆] \n", "\n", "💬 simple 答：\n", " 虽然我主要关注Python编程，但可以给你一些通用的建议：\n", "\n", "1. **制定预算**：记录收入和支出，找出可以削减的开支。\n", "2. **寻找额外收入**：考虑兼职或自由职业，利用你的技能（如编程、设计等）。\n", "3. **学习新技能**：利用免费资源学习新技能，提升自己的市场竞争力。\n", "4. **寻求帮助**：与朋友或家人沟通，看看是否可以获得支持。\n", "5. **利用社区资源**：查找当地的支持项目或资源，帮助渡过难关。\n", "\n", "如果你有编程方面的问题，随时问我！ \n", "\n", "\n", "🧠 Memory 观测 - Round 3:\n", "—— Memory.get() (最终传给LLM的消息) ——\n", "[0] MessageRole.SYSTEM: <memory>\n", "<course_policy>\n", "【课程背景】Python 初学者教程；【助教规则】优先基于检索到的文档回答；无法从文档找到答案时，明确说明不知道；回答要举【最小可运行】示例、避免过度抽象；语言：中文；风格：清晰、简短、分点说明。\n", "</course_policy>\n", "<learner_facts>\n", "<fact>用户的公司叫 ZenFlow</fact>\n", "<fact>用户偏好多用代码示例进行教学</fact>\n", "<fact>用户对HTML、CSS、JS了解比较多</fact>\n", "<fact>用户的目标是拯救全世界</fact>\n", "</learner_facts>\n", "</memory>\n", "[1] MessageRole.USER: 说说没钱的时候应该怎么办？\n", "[2] MessageRole.ASSISTANT: 虽然我主要关注Python编程，但可以给你一些通用的建议：\n", "\n", "1. **制定预算**：记录收入和支出，找出可以削减的开支。\n", "2. **寻找额外收入**：考虑兼职或自由职业，利用你的技能（如编程、设计等）。\n", "3. **学习新技能**：利用免费资源学习新技能，提升自己的市场竞争力。\n", "4. **寻求帮助**：与朋友或家人沟通，看看是否可以获得支持。\n", "5. **利用社区资源**：查找当地的支持项目或资源，帮助渡过难关。\n", "\n", "如果你有编程方面的问题，随时问我！\n", "\n", "—— Memory Blocks 详情 ——\n", "[1] course_policy <StaticMemoryBlock>\n", "  • static_content: [TextBlock(block_type='text', text='【课程背景】Python 初学者教程；【助教规则】优先基于检索到的文档回答；无法从文档找到答案时，明确说明不知道；回答要举【最小可运行】示例、避免过度抽象；语言：中文；风格：清晰、简短、分点说明。')]\n", "[2] learner_facts <FactExtractionMemoryBlock>\n", "  • facts 数量: 4\n", "    [1] 用户的公司叫 ZenFlow\n", "    [2] 用户偏好多用代码示例进行教学\n", "    [3] 用户对HTML、CSS、JS了解比较多\n", "    [4] 用户的目标是拯救全世界\n", "\n", "—— Memory 统计信息 ——\n", "session_id: demo-python-rag\n", "token_limit: 1000\n", "token_flush_size: 200\n", "chat_history_token_ratio: 0.6\n", "------------------------------------------------------------------------------------------\n", "==========================================================================================\n", "\n"]}], "source": ["# === 多轮问答（RAG + 共享记忆） ================================================\n", "from llama_index.core.llms import ChatMessage, MessageRole\n", "\n", "def print_memory_details(memory, round_name=\"\"):\n", "    \"\"\"完整输出memory的详细信息\"\"\"\n", "    print(f\"\\n🧠 Memory 观测 - {round_name}:\")\n", "    print(\"—— Memory.get() (最终传给LLM的消息) ——\")\n", "    for i, m in enumerate(memory.get()):\n", "        role = getattr(m, \"role\", \"\")\n", "        content = getattr(m, \"content\", \"\")\n", "        print(f\"[{i}] {role}: {content}\")  # 完整输出，不截断\n", "    \n", "    print(\"\\n—— Memory Blocks 详情 ——\")\n", "    for i, blk in enumerate(memory.memory_blocks, 1):\n", "        print(f\"[{i}] {blk.name} <{type(blk).__name__}>\")\n", "        if hasattr(blk, \"static_content\"):\n", "            print(f\"  • static_content: {blk.static_content}\")\n", "        if hasattr(blk, \"facts\"):\n", "            facts = getattr(blk, 'facts', [])\n", "            print(f\"  • facts 数量: {len(facts)}\")\n", "            for j, fact in enumerate(facts, 1):\n", "                print(f\"    [{j}] {fact}\")  # 完整输出每个fact\n", "    \n", "    print(\"\\n—— Memory 统计信息 ——\")\n", "    print(f\"session_id: {memory.session_id}\")\n", "    print(f\"token_limit: {memory.token_limit}\")\n", "    print(f\"token_flush_size: {memory.token_flush_size}\")\n", "    print(f\"chat_history_token_ratio: {memory.chat_history_token_ratio}\")\n", "    print(\"-\" * 90)\n", "\n", "print(\"\\n\" + \"=\"*90)\n", "print(\"🗣️ [Round 1 - RAG] \")\n", "resp1 = condense_engine.chat(\"如何理解python中所说的参数，感觉是个很难理解的概念呢！\")\n", "print(\"\\n🔎 condense_question 答：\\n\", resp1, \"\\n\")\n", "\n", "print_memory_details(memory, \"Round 1\")\n", "\n", "print(\"🗣️ [Round 2 - 记录事实] \")\n", "resp2 = condense_engine.chat(\"我对html、css、js了解比较多，还有，我的目标是拯救全世界，你要记住。\")\n", "print(\"\\n🧠 答：\\n\", resp2, \"\\n\")\n", "print_memory_details(memory, \"Round 2\")\n", "\n", "\n", "print(\"🗣️ [Round 2.5 - 追加事实] \")\n", "resp2_5 = condense_engine.chat(\"你在跟我讲解的时候要用理查德·费曼的风格，你要记住。\")\n", "print_memory_details(memory, \"Round 2.5\")\n", "\n", "\n", "\n", "print(\"🗣️ [Round 3 - 无检索，仅用记忆] \")\n", "resp3 = simple_engine.chat(\"说说没钱的时候应该怎么办？\")\n", "print(\"\\n💬 simple 答：\\n\", resp3, \"\\n\")\n", "\n", "print_memory_details(memory, \"Round 3\")\n", "\n", "print(\"=\"*90 + \"\\n\")"]}, {"cell_type": "code", "execution_count": 12, "id": "0d672449", "metadata": {}, "outputs": [{"data": {"text/html": ["<script type=\"esms-options\">{\"shimMode\": true}</script><style>*[data-root-id],\n", "*[data-root-id] > * {\n", "  box-sizing: border-box;\n", "  font-family: var(--jp-ui-font-family);\n", "  font-size: var(--jp-ui-font-size1);\n", "  color: var(--vscode-editor-foreground, var(--jp-ui-font-color1));\n", "}\n", "\n", "/* Override VSCode background color */\n", ".cell-output-ipywidget-background:has(\n", "  > .cell-output-ipywidget-background > .lm-Widget > *[data-root-id]\n", "),\n", ".cell-output-ipywidget-background:has(> .lm-Widget > *[data-root-id]) {\n", "  background-color: transparent !important;\n", "}\n", "</style>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/javascript": "(function(root) {\n  function now() {\n    return new Date();\n  }\n\n  const force = true;\n  const py_version = '3.7.3'.replace('rc', '-rc.').replace('.dev', '-dev.');\n  const reloading = false;\n  const Bokeh = root.Bokeh;\n\n  // Set a timeout for this load but only if we are not already initializing\n  if (typeof (root._bokeh_timeout) === \"undefined\" || (force || !root._bokeh_is_initializing)) {\n    root._bokeh_timeout = Date.now() + 5000;\n    root._bokeh_failed_load = false;\n  }\n\n  function run_callbacks() {\n    try {\n      root._bokeh_onload_callbacks.forEach(function(callback) {\n        if (callback != null)\n          callback();\n      });\n    } finally {\n      delete root._bokeh_onload_callbacks;\n    }\n    console.debug(\"Bokeh: all callbacks have finished\");\n  }\n\n  function load_libs(css_urls, js_urls, js_modules, js_exports, callback) {\n    if (css_urls == null) css_urls = [];\n    if (js_urls == null) js_urls = [];\n    if (js_modules == null) js_modules = [];\n    if (js_exports == null) js_exports = {};\n\n    root._bokeh_onload_callbacks.push(callback);\n\n    if (root._bokeh_is_loading > 0) {\n      // Don't load bokeh if it is still initializing\n      console.debug(\"Bokeh: BokehJS is being loaded, scheduling callback at\", now());\n      return null;\n    } else if (js_urls.length === 0 && js_modules.length === 0 && Object.keys(js_exports).length === 0) {\n      // There is nothing to load\n      run_callbacks();\n      return null;\n    }\n\n    function on_load() {\n      root._bokeh_is_loading--;\n      if (root._bokeh_is_loading === 0) {\n        console.debug(\"Bokeh: all BokehJS libraries/stylesheets loaded\");\n        run_callbacks()\n      }\n    }\n    window._bokeh_on_load = on_load\n\n    function on_error(e) {\n      const src_el = e.srcElement\n      console.error(\"failed to load \" + (src_el.href || src_el.src));\n    }\n\n    const skip = [];\n    if (window.requirejs) {\n      window.requirejs.config({'packages': {}, 'paths': {}, 'shim': {}});\n      root._bokeh_is_loading = css_urls.length + 0;\n    } else {\n      root._bokeh_is_loading = css_urls.length + js_urls.length + js_modules.length + Object.keys(js_exports).length;\n    }\n\n    const existing_stylesheets = []\n    const links = document.getElementsByTagName('link')\n    for (let i = 0; i < links.length; i++) {\n      const link = links[i]\n      if (link.href != null) {\n        existing_stylesheets.push(link.href)\n      }\n    }\n    for (let i = 0; i < css_urls.length; i++) {\n      const url = css_urls[i];\n      const escaped = encodeURI(url)\n      if (existing_stylesheets.indexOf(escaped) !== -1) {\n        on_load()\n        continue;\n      }\n      const element = document.createElement(\"link\");\n      element.onload = on_load;\n      element.onerror = on_error;\n      element.rel = \"stylesheet\";\n      element.type = \"text/css\";\n      element.href = url;\n      console.debug(\"Bokeh: injecting link tag for BokehJS stylesheet: \", url);\n      document.body.appendChild(element);\n    }    var existing_scripts = []\n    const scripts = document.getElementsByTagName('script')\n    for (let i = 0; i < scripts.length; i++) {\n      var script = scripts[i]\n      if (script.src != null) {\n        existing_scripts.push(script.src)\n      }\n    }\n    for (let i = 0; i < js_urls.length; i++) {\n      const url = js_urls[i];\n      const escaped = encodeURI(url)\n      if (skip.indexOf(escaped) !== -1 || existing_scripts.indexOf(escaped) !== -1) {\n        if (!window.requirejs) {\n          on_load();\n        }\n        continue;\n      }\n      const element = document.createElement('script');\n      element.onload = on_load;\n      element.onerror = on_error;\n      element.async = false;\n      element.src = url;\n      console.debug(\"Bokeh: injecting script tag for BokehJS library: \", url);\n      document.head.appendChild(element);\n    }\n    for (let i = 0; i < js_modules.length; i++) {\n      const url = js_modules[i];\n      const escaped = encodeURI(url)\n      if (skip.indexOf(escaped) !== -1 || existing_scripts.indexOf(escaped) !== -1) {\n        if (!window.requirejs) {\n          on_load();\n        }\n        continue;\n      }\n      var element = document.createElement('script');\n      element.onload = on_load;\n      element.onerror = on_error;\n      element.async = false;\n      element.src = url;\n      element.type = \"module\";\n      console.debug(\"Bokeh: injecting script tag for BokehJS library: \", url);\n      document.head.appendChild(element);\n    }\n    for (const name in js_exports) {\n      const url = js_exports[name];\n      const escaped = encodeURI(url)\n      if (skip.indexOf(escaped) >= 0 || root[name] != null) {\n        if (!window.requirejs) {\n          on_load();\n        }\n        continue;\n      }\n      var element = document.createElement('script');\n      element.onerror = on_error;\n      element.async = false;\n      element.type = \"module\";\n      console.debug(\"Bokeh: injecting script tag for BokehJS library: \", url);\n      element.textContent = `\n      import ${name} from \"${url}\"\n      window.${name} = ${name}\n      window._bokeh_on_load()\n      `\n      document.head.appendChild(element);\n    }\n    if (!js_urls.length && !js_modules.length) {\n      on_load()\n    }\n  };\n\n  function inject_raw_css(css) {\n    const element = document.createElement(\"style\");\n    element.appendChild(document.createTextNode(css));\n    document.body.appendChild(element);\n  }\n\n  const js_urls = [\"https://cdn.holoviz.org/panel/1.7.5/dist/bundled/reactiveesm/es-module-shims@^1.10.0/dist/es-module-shims.min.js\", \"https://cdn.bokeh.org/bokeh/release/bokeh-3.7.3.min.js\", \"https://cdn.bokeh.org/bokeh/release/bokeh-gl-3.7.3.min.js\", \"https://cdn.bokeh.org/bokeh/release/bokeh-widgets-3.7.3.min.js\", \"https://cdn.bokeh.org/bokeh/release/bokeh-tables-3.7.3.min.js\", \"https://cdn.holoviz.org/panel/1.7.5/dist/panel.min.js\"];\n  const js_modules = [];\n  const js_exports = {};\n  const css_urls = [];\n  const inline_js = [    function(Bokeh) {\n      Bokeh.set_log_level(\"info\");\n    },\nfunction(Bokeh) {} // ensure no trailing comma for IE\n  ];\n\n  function run_inline_js() {\n    if ((root.Bokeh !== undefined) || (force === true)) {\n      for (let i = 0; i < inline_js.length; i++) {\n        try {\n          inline_js[i].call(root, root.Bokeh);\n        } catch(e) {\n          if (!reloading) {\n            throw e;\n          }\n        }\n      }\n      // Cache old bokeh versions\n      if (Bokeh != undefined && !reloading) {\n        var NewBokeh = root.Bokeh;\n        if (Bokeh.versions === undefined) {\n          Bokeh.versions = new Map();\n        }\n        if (NewBokeh.version !== Bokeh.version) {\n          Bokeh.versions.set(NewBokeh.version, NewBokeh)\n        }\n        root.Bokeh = Bokeh;\n      }\n    } else if (Date.now() < root._bokeh_timeout) {\n      setTimeout(run_inline_js, 100);\n    } else if (!root._bokeh_failed_load) {\n      console.log(\"Bokeh: BokehJS failed to load within specified timeout.\");\n      root._bokeh_failed_load = true;\n    }\n    root._bokeh_is_initializing = false\n  }\n\n  function load_or_wait() {\n    // Implement a backoff loop that tries to ensure we do not load multiple\n    // versions of Bokeh and its dependencies at the same time.\n    // In recent versions we use the root._bokeh_is_initializing flag\n    // to determine whether there is an ongoing attempt to initialize\n    // bokeh, however for backward compatibility we also try to ensure\n    // that we do not start loading a newer (Panel>=1.0 and Bokeh>3) version\n    // before older versions are fully initialized.\n    if (root._bokeh_is_initializing && Date.now() > root._bokeh_timeout) {\n      // If the timeout and bokeh was not successfully loaded we reset\n      // everything and try loading again\n      root._bokeh_timeout = Date.now() + 5000;\n      root._bokeh_is_initializing = false;\n      root._bokeh_onload_callbacks = undefined;\n      root._bokeh_is_loading = 0\n      console.log(\"Bokeh: BokehJS was loaded multiple times but one version failed to initialize.\");\n      load_or_wait();\n    } else if (root._bokeh_is_initializing || (typeof root._bokeh_is_initializing === \"undefined\" && root._bokeh_onload_callbacks !== undefined)) {\n      setTimeout(load_or_wait, 100);\n    } else {\n      root._bokeh_is_initializing = true\n      root._bokeh_onload_callbacks = []\n      const bokeh_loaded = root.Bokeh != null && (root.Bokeh.version === py_version || (root.Bokeh.versions !== undefined && root.Bokeh.versions.has(py_version)));\n      if (!reloading && !bokeh_loaded) {\n        if (root.Bokeh) {\n          root.Bokeh = undefined;\n        }\n        console.debug(\"Bokeh: BokehJS not loaded, scheduling load and callback at\", now());\n      }\n      load_libs(css_urls, js_urls, js_modules, js_exports, function() {\n        console.debug(\"Bokeh: BokehJS plotting callback run at\", now());\n        run_inline_js();\n      });\n    }\n  }\n  // Give older versions of the autoload script a head-start to ensure\n  // they initialize before we start loading newer version.\n  setTimeout(load_or_wait, 100)\n}(window));", "application/vnd.holoviews_load.v0+json": ""}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/javascript": "\nif ((window.PyViz === undefined) || (window.PyViz instanceof HTMLElement)) {\n  window.PyViz = {comms: {}, comm_status:{}, kernels:{}, receivers: {}, plot_index: []}\n}\n\n\n    function JupyterCommManager() {\n    }\n\n    JupyterCommManager.prototype.register_target = function(plot_id, comm_id, msg_handler) {\n      if (window.comm_manager || ((window.Jupyter !== undefined) && (Jupyter.notebook.kernel != null))) {\n        var comm_manager = window.comm_manager || Jupyter.notebook.kernel.comm_manager;\n        comm_manager.register_target(comm_id, function(comm) {\n          comm.on_msg(msg_handler);\n        });\n      } else if ((plot_id in window.PyViz.kernels) && (window.PyViz.kernels[plot_id])) {\n        window.PyViz.kernels[plot_id].registerCommTarget(comm_id, function(comm) {\n          comm.onMsg = msg_handler;\n        });\n      } else if (typeof google != 'undefined' && google.colab.kernel != null) {\n        google.colab.kernel.comms.registerTarget(comm_id, (comm) => {\n          var messages = comm.messages[Symbol.asyncIterator]();\n          function processIteratorResult(result) {\n            var message = result.value;\n            var content = {data: message.data, comm_id};\n            var buffers = []\n            for (var buffer of message.buffers || []) {\n              buffers.push(new DataView(buffer))\n            }\n            var metadata = message.metadata || {};\n            var msg = {content, buffers, metadata}\n            msg_handler(msg);\n            return messages.next().then(processIteratorResult);\n          }\n          return messages.next().then(processIteratorResult);\n        })\n      }\n    }\n\n    JupyterCommManager.prototype.get_client_comm = function(plot_id, comm_id, msg_handler) {\n      if (comm_id in window.PyViz.comms) {\n        return window.PyViz.comms[comm_id];\n      } else if (window.comm_manager || ((window.Jupyter !== undefined) && (Jupyter.notebook.kernel != null))) {\n        var comm_manager = window.comm_manager || Jupyter.notebook.kernel.comm_manager;\n        var comm = comm_manager.new_comm(comm_id, {}, {}, {}, comm_id);\n        if (msg_handler) {\n          comm.on_msg(msg_handler);\n        }\n      } else if ((plot_id in window.PyViz.kernels) && (window.PyViz.kernels[plot_id])) {\n        var comm = window.PyViz.kernels[plot_id].connectToComm(comm_id);\n        let retries = 0;\n        const open = () => {\n          if (comm.active) {\n            comm.open();\n          } else if (retries > 3) {\n            console.warn('Comm target never activated')\n          } else {\n            retries += 1\n            setTimeout(open, 500)\n          }\n        }\n        if (comm.active) {\n          comm.open();\n        } else {\n          setTimeout(open, 500)\n        }\n        if (msg_handler) {\n          comm.onMsg = msg_handler;\n        }\n      } else if (typeof google != 'undefined' && google.colab.kernel != null) {\n        var comm_promise = google.colab.kernel.comms.open(comm_id)\n        comm_promise.then((comm) => {\n          window.PyViz.comms[comm_id] = comm;\n          if (msg_handler) {\n            var messages = comm.messages[Symbol.asyncIterator]();\n            function processIteratorResult(result) {\n              var message = result.value;\n              var content = {data: message.data};\n              var metadata = message.metadata || {comm_id};\n              var msg = {content, metadata}\n              msg_handler(msg);\n              return messages.next().then(processIteratorResult);\n            }\n            return messages.next().then(processIteratorResult);\n          }\n        })\n        var sendClosure = (data, metadata, buffers, disposeOnDone) => {\n          return comm_promise.then((comm) => {\n            comm.send(data, metadata, buffers, disposeOnDone);\n          });\n        };\n        var comm = {\n          send: sendClosure\n        };\n      }\n      window.PyViz.comms[comm_id] = comm;\n      return comm;\n    }\n    window.PyViz.comm_manager = new JupyterCommManager();\n    \n\n\nvar JS_MIME_TYPE = 'application/javascript';\nvar HTML_MIME_TYPE = 'text/html';\nvar EXEC_MIME_TYPE = 'application/vnd.holoviews_exec.v0+json';\nvar CLASS_NAME = 'output';\n\n/**\n * Render data to the DOM node\n */\nfunction render(props, node) {\n  var div = document.createElement(\"div\");\n  var script = document.createElement(\"script\");\n  node.appendChild(div);\n  node.appendChild(script);\n}\n\n/**\n * Handle when a new output is added\n */\nfunction handle_add_output(event, handle) {\n  var output_area = handle.output_area;\n  var output = handle.output;\n  if ((output.data == undefined) || (!output.data.hasOwnProperty(EXEC_MIME_TYPE))) {\n    return\n  }\n  var id = output.metadata[EXEC_MIME_TYPE][\"id\"];\n  var toinsert = output_area.element.find(\".\" + CLASS_NAME.split(' ')[0]);\n  if (id !== undefined) {\n    var nchildren = toinsert.length;\n    var html_node = toinsert[nchildren-1].children[0];\n    html_node.innerHTML = output.data[HTML_MIME_TYPE];\n    var scripts = [];\n    var nodelist = html_node.querySelectorAll(\"script\");\n    for (var i in nodelist) {\n      if (nodelist.hasOwnProperty(i)) {\n        scripts.push(nodelist[i])\n      }\n    }\n\n    scripts.forEach( function (oldScript) {\n      var newScript = document.createElement(\"script\");\n      var attrs = [];\n      var nodemap = oldScript.attributes;\n      for (var j in nodemap) {\n        if (nodemap.hasOwnProperty(j)) {\n          attrs.push(nodemap[j])\n        }\n      }\n      attrs.forEach(function(attr) { newScript.setAttribute(attr.name, attr.value) });\n      newScript.appendChild(document.createTextNode(oldScript.innerHTML));\n      oldScript.parentNode.replaceChild(newScript, oldScript);\n    });\n    if (JS_MIME_TYPE in output.data) {\n      toinsert[nchildren-1].children[1].textContent = output.data[JS_MIME_TYPE];\n    }\n    output_area._hv_plot_id = id;\n    if ((window.Bokeh !== undefined) && (id in Bokeh.index)) {\n      window.PyViz.plot_index[id] = Bokeh.index[id];\n    } else {\n      window.PyViz.plot_index[id] = null;\n    }\n  } else if (output.metadata[EXEC_MIME_TYPE][\"server_id\"] !== undefined) {\n    var bk_div = document.createElement(\"div\");\n    bk_div.innerHTML = output.data[HTML_MIME_TYPE];\n    var script_attrs = bk_div.children[0].attributes;\n    for (var i = 0; i < script_attrs.length; i++) {\n      toinsert[toinsert.length - 1].childNodes[1].setAttribute(script_attrs[i].name, script_attrs[i].value);\n    }\n    // store reference to server id on output_area\n    output_area._bokeh_server_id = output.metadata[EXEC_MIME_TYPE][\"server_id\"];\n  }\n}\n\n/**\n * Handle when an output is cleared or removed\n */\nfunction handle_clear_output(event, handle) {\n  var id = handle.cell.output_area._hv_plot_id;\n  var server_id = handle.cell.output_area._bokeh_server_id;\n  if (((id === undefined) || !(id in PyViz.plot_index)) && (server_id !== undefined)) { return; }\n  var comm = window.PyViz.comm_manager.get_client_comm(\"hv-extension-comm\", \"hv-extension-comm\", function () {});\n  if (server_id !== null) {\n    comm.send({event_type: 'server_delete', 'id': server_id});\n    return;\n  } else if (comm !== null) {\n    comm.send({event_type: 'delete', 'id': id});\n  }\n  delete PyViz.plot_index[id];\n  if ((window.Bokeh !== undefined) & (id in window.Bokeh.index)) {\n    var doc = window.Bokeh.index[id].model.document\n    doc.clear();\n    const i = window.Bokeh.documents.indexOf(doc);\n    if (i > -1) {\n      window.Bokeh.documents.splice(i, 1);\n    }\n  }\n}\n\n/**\n * Handle kernel restart event\n */\nfunction handle_kernel_cleanup(event, handle) {\n  delete PyViz.comms[\"hv-extension-comm\"];\n  window.PyViz.plot_index = {}\n}\n\n/**\n * Handle update_display_data messages\n */\nfunction handle_update_output(event, handle) {\n  handle_clear_output(event, {cell: {output_area: handle.output_area}})\n  handle_add_output(event, handle)\n}\n\nfunction register_renderer(events, OutputArea) {\n  function append_mime(data, metadata, element) {\n    // create a DOM node to render to\n    var toinsert = this.create_output_subarea(\n    metadata,\n    CLASS_NAME,\n    EXEC_MIME_TYPE\n    );\n    this.keyboard_manager.register_events(toinsert);\n    // Render to node\n    var props = {data: data, metadata: metadata[EXEC_MIME_TYPE]};\n    render(props, toinsert[0]);\n    element.append(toinsert);\n    return toinsert\n  }\n\n  events.on('output_added.OutputArea', handle_add_output);\n  events.on('output_updated.OutputArea', handle_update_output);\n  events.on('clear_output.CodeCell', handle_clear_output);\n  events.on('delete.Cell', handle_clear_output);\n  events.on('kernel_ready.Kernel', handle_kernel_cleanup);\n\n  OutputArea.prototype.register_mime_type(EXEC_MIME_TYPE, append_mime, {\n    safe: true,\n    index: 0\n  });\n}\n\nif (window.Jupyter !== undefined) {\n  try {\n    var events = require('base/js/events');\n    var OutputArea = require('notebook/js/outputarea').OutputArea;\n    if (OutputArea.prototype.mime_types().indexOf(EXEC_MIME_TYPE) == -1) {\n      register_renderer(events, OutputArea);\n    }\n  } catch(err) {\n  }\n}\n", "application/vnd.holoviews_load.v0+json": ""}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.holoviews_exec.v0+json": "", "text/html": ["<div id='d3b9c226-34de-497e-9184-ed37a2ef1182'>\n", "  <div id=\"bbe3badd-5e2b-45d0-b4f6-b4483f33b5d6\" data-root-id=\"d3b9c226-34de-497e-9184-ed37a2ef1182\" style=\"display: contents;\"></div>\n", "</div>\n", "<script type=\"application/javascript\">(function(root) {\n", "  var docs_json = {\"df7392f9-8a35-4040-aeb4-471d36c47d24\":{\"version\":\"3.7.3\",\"title\":\"Bokeh Application\",\"roots\":[{\"type\":\"object\",\"name\":\"panel.models.browser.BrowserInfo\",\"id\":\"d3b9c226-34de-497e-9184-ed37a2ef1182\"},{\"type\":\"object\",\"name\":\"panel.models.comm_manager.CommManager\",\"id\":\"2d501996-9fa8-4f64-89a0-e0349b28dcd3\",\"attributes\":{\"plot_id\":\"d3b9c226-34de-497e-9184-ed37a2ef1182\",\"comm_id\":\"1d6366513fa24333b53a81d7684cb8a9\",\"client_comm_id\":\"2f6b3fe7024a4306a27b9b4f8c22bcd6\"}}],\"defs\":[{\"type\":\"model\",\"name\":\"ReactiveHTML1\"},{\"type\":\"model\",\"name\":\"FlexBox1\",\"properties\":[{\"name\":\"align_content\",\"kind\":\"Any\",\"default\":\"flex-start\"},{\"name\":\"align_items\",\"kind\":\"Any\",\"default\":\"flex-start\"},{\"name\":\"flex_direction\",\"kind\":\"Any\",\"default\":\"row\"},{\"name\":\"flex_wrap\",\"kind\":\"Any\",\"default\":\"wrap\"},{\"name\":\"gap\",\"kind\":\"Any\",\"default\":\"\"},{\"name\":\"justify_content\",\"kind\":\"Any\",\"default\":\"flex-start\"}]},{\"type\":\"model\",\"name\":\"FloatPanel1\",\"properties\":[{\"name\":\"config\",\"kind\":\"Any\",\"default\":{\"type\":\"map\"}},{\"name\":\"contained\",\"kind\":\"Any\",\"default\":true},{\"name\":\"position\",\"kind\":\"Any\",\"default\":\"right-top\"},{\"name\":\"offsetx\",\"kind\":\"Any\",\"default\":null},{\"name\":\"offsety\",\"kind\":\"Any\",\"default\":null},{\"name\":\"theme\",\"kind\":\"Any\",\"default\":\"primary\"},{\"name\":\"status\",\"kind\":\"Any\",\"default\":\"normalized\"}]},{\"type\":\"model\",\"name\":\"GridStack1\",\"properties\":[{\"name\":\"ncols\",\"kind\":\"Any\",\"default\":null},{\"name\":\"nrows\",\"kind\":\"Any\",\"default\":null},{\"name\":\"allow_resize\",\"kind\":\"Any\",\"default\":true},{\"name\":\"allow_drag\",\"kind\":\"Any\",\"default\":true},{\"name\":\"state\",\"kind\":\"Any\",\"default\":[]}]},{\"type\":\"model\",\"name\":\"drag1\",\"properties\":[{\"name\":\"slider_width\",\"kind\":\"Any\",\"default\":5},{\"name\":\"slider_color\",\"kind\":\"Any\",\"default\":\"black\"},{\"name\":\"value\",\"kind\":\"Any\",\"default\":50}]},{\"type\":\"model\",\"name\":\"click1\",\"properties\":[{\"name\":\"terminal_output\",\"kind\":\"Any\",\"default\":\"\"},{\"name\":\"debug_name\",\"kind\":\"Any\",\"default\":\"\"},{\"name\":\"clears\",\"kind\":\"Any\",\"default\":0}]},{\"type\":\"model\",\"name\":\"ReactiveESM1\",\"properties\":[{\"name\":\"esm_constants\",\"kind\":\"Any\",\"default\":{\"type\":\"map\"}}]},{\"type\":\"model\",\"name\":\"JSComponent1\",\"properties\":[{\"name\":\"esm_constants\",\"kind\":\"Any\",\"default\":{\"type\":\"map\"}}]},{\"type\":\"model\",\"name\":\"ReactComponent1\",\"properties\":[{\"name\":\"use_shadow_dom\",\"kind\":\"Any\",\"default\":true},{\"name\":\"esm_constants\",\"kind\":\"Any\",\"default\":{\"type\":\"map\"}}]},{\"type\":\"model\",\"name\":\"AnyWidgetComponent1\",\"properties\":[{\"name\":\"use_shadow_dom\",\"kind\":\"Any\",\"default\":true},{\"name\":\"esm_constants\",\"kind\":\"Any\",\"default\":{\"type\":\"map\"}}]},{\"type\":\"model\",\"name\":\"FastWrapper1\",\"properties\":[{\"name\":\"object\",\"kind\":\"Any\",\"default\":null},{\"name\":\"style\",\"kind\":\"Any\",\"default\":null}]},{\"type\":\"model\",\"name\":\"NotificationArea1\",\"properties\":[{\"name\":\"js_events\",\"kind\":\"Any\",\"default\":{\"type\":\"map\"}},{\"name\":\"max_notifications\",\"kind\":\"Any\",\"default\":5},{\"name\":\"notifications\",\"kind\":\"Any\",\"default\":[]},{\"name\":\"position\",\"kind\":\"Any\",\"default\":\"bottom-right\"},{\"name\":\"_clear\",\"kind\":\"Any\",\"default\":0},{\"name\":\"types\",\"kind\":\"Any\",\"default\":[{\"type\":\"map\",\"entries\":[[\"type\",\"warning\"],[\"background\",\"#ffc107\"],[\"icon\",{\"type\":\"map\",\"entries\":[[\"className\",\"fas fa-exclamation-triangle\"],[\"tagName\",\"i\"],[\"color\",\"white\"]]}]]},{\"type\":\"map\",\"entries\":[[\"type\",\"info\"],[\"background\",\"#007bff\"],[\"icon\",{\"type\":\"map\",\"entries\":[[\"className\",\"fas fa-info-circle\"],[\"tagName\",\"i\"],[\"color\",\"white\"]]}]]}]}]},{\"type\":\"model\",\"name\":\"Notification\",\"properties\":[{\"name\":\"background\",\"kind\":\"Any\",\"default\":null},{\"name\":\"duration\",\"kind\":\"Any\",\"default\":3000},{\"name\":\"icon\",\"kind\":\"Any\",\"default\":null},{\"name\":\"message\",\"kind\":\"Any\",\"default\":\"\"},{\"name\":\"notification_type\",\"kind\":\"Any\",\"default\":null},{\"name\":\"_rendered\",\"kind\":\"Any\",\"default\":false},{\"name\":\"_destroyed\",\"kind\":\"Any\",\"default\":false}]},{\"type\":\"model\",\"name\":\"TemplateActions1\",\"properties\":[{\"name\":\"open_modal\",\"kind\":\"Any\",\"default\":0},{\"name\":\"close_modal\",\"kind\":\"Any\",\"default\":0}]},{\"type\":\"model\",\"name\":\"BootstrapTemplateActions1\",\"properties\":[{\"name\":\"open_modal\",\"kind\":\"Any\",\"default\":0},{\"name\":\"close_modal\",\"kind\":\"Any\",\"default\":0}]},{\"type\":\"model\",\"name\":\"TemplateEditor1\",\"properties\":[{\"name\":\"layout\",\"kind\":\"Any\",\"default\":[]}]},{\"type\":\"model\",\"name\":\"MaterialTemplateActions1\",\"properties\":[{\"name\":\"open_modal\",\"kind\":\"Any\",\"default\":0},{\"name\":\"close_modal\",\"kind\":\"Any\",\"default\":0}]},{\"type\":\"model\",\"name\":\"request_value1\",\"properties\":[{\"name\":\"fill\",\"kind\":\"Any\",\"default\":\"none\"},{\"name\":\"_synced\",\"kind\":\"Any\",\"default\":null},{\"name\":\"_request_sync\",\"kind\":\"Any\",\"default\":0}]}]}};\n", "  var render_items = [{\"docid\":\"df7392f9-8a35-4040-aeb4-471d36c47d24\",\"roots\":{\"d3b9c226-34de-497e-9184-ed37a2ef1182\":\"bbe3badd-5e2b-45d0-b4f6-b4483f33b5d6\"},\"root_ids\":[\"d3b9c226-34de-497e-9184-ed37a2ef1182\"]}];\n", "  var docs = Object.values(docs_json)\n", "  if (!docs) {\n", "    return\n", "  }\n", "  const py_version = docs[0].version.replace('rc', '-rc.').replace('.dev', '-dev.')\n", "  async function embed_document(root) {\n", "    var Bokeh = get_bokeh(root)\n", "    await Bokeh.embed.embed_items_notebook(docs_json, render_items);\n", "    for (const render_item of render_items) {\n", "      for (const root_id of render_item.root_ids) {\n", "\tconst id_el = document.getElementById(root_id)\n", "\tif (id_el.children.length && id_el.children[0].hasAttribute('data-root-id')) {\n", "\t  const root_el = id_el.children[0]\n", "\t  root_el.id = root_el.id + '-rendered'\n", "\t  for (const child of root_el.children) {\n", "            // Ensure JupyterLab does not capture keyboard shortcuts\n", "            // see: https://jupyterlab.readthedocs.io/en/4.1.x/extension/notebook.html#keyboard-interaction-model\n", "\t    child.setAttribute('data-lm-suppress-shortcuts', 'true')\n", "\t  }\n", "\t}\n", "      }\n", "    }\n", "  }\n", "  function get_bokeh(root) {\n", "    if (root.Bokeh === undefined) {\n", "      return null\n", "    } else if (root.Bokeh.version !== py_version) {\n", "      if (root.Bokeh.versions === undefined || !root.Bokeh.versions.has(py_version)) {\n", "\treturn null\n", "      }\n", "      return root.Bokeh.versions.get(py_version);\n", "    } else if (root.Bokeh.version === py_version) {\n", "      return root.<PERSON><PERSON><PERSON>\n", "    }\n", "    return null\n", "  }\n", "  function is_loaded(root) {\n", "    var Bokeh = get_bokeh(root)\n", "    return (Bokeh != null && Bokeh.Panel !== undefined)\n", "  }\n", "  if (is_loaded(root)) {\n", "    embed_document(root);\n", "  } else {\n", "    var attempts = 0;\n", "    var timer = setInterval(function(root) {\n", "      if (is_loaded(root)) {\n", "        clearInterval(timer);\n", "        embed_document(root);\n", "      } else if (document.readyState == \"complete\") {\n", "        attempts++;\n", "        if (attempts > 200) {\n", "          clearInterval(timer);\n", "\t  var Bokeh = get_bokeh(root)\n", "\t  if (Bokeh == null || Bokeh.Panel == null) {\n", "            console.warn(\"Panel: ERROR: Unable to run Panel code because Bokeh or Panel library is missing\");\n", "\t  } else {\n", "\t    console.warn(\"Panel: WARNING: Attempting to render but not all required libraries could be resolved.\")\n", "\t    embed_document(root)\n", "\t  }\n", "        }\n", "      }\n", "    }, 25, root)\n", "  }\n", "})(window);</script>"]}, "metadata": {"application/vnd.holoviews_exec.v0+json": {"id": "d3b9c226-34de-497e-9184-ed37a2ef1182"}}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">╭──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">│ 🗣️ [Round 1 - RAG] 如何理解python中所说的参数                                                                         │</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;34m╭──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\u001b[0m\n", "\u001b[1;34m│\u001b[0m\u001b[1;34m \u001b[0m\u001b[1;34m🗣️ [Round 1 - RAG] 如何理解python中所说的参数\u001b[0m\u001b[1;34m                                                                        \u001b[0m\u001b[1;34m \u001b[0m\u001b[1;34m│\u001b[0m\n", "\u001b[1;34m╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Querying with: 在Python中，参数是什么？为什么这个概念会让人觉得难以理解？\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">╭──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│ 🔎 condense_question 答：                                                                                            │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│ 在Python中，参数是函数定义中用于接收输入信息的变量。参数分为形参和实参。形参是函数定义时指定的变量，而实参是在调用函 │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│ 数时传递给这些形参的具体值。这个概念可能让人觉得难以理解的原因在于，形参和实参的名称和角色容易混淆，尤其是在讨论函数 │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│ 时，有时会不小心将它们混用。此外，函数调用时实参的顺序、数量以及使用关键字实参的方式也可能导致错误，从而增加了理解的 │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│ 复杂性。                                                                                                             │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[32m╭──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m🔎 condense_question 答：\u001b[0m\u001b[32m                                                                                           \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m在Python中，参数是函数定义中用于接收输入信息的变量。参数分为形参和实参。形参是函数定义时指定的变量，而实参是在调用函\u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m数时传递给这些形参的具体值。这个概念可能让人觉得难以理解的原因在于，形参和实参的名称和角色容易混淆，尤其是在讨论函数\u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m时，有时会不小心将它们混用。此外，函数调用时实参的顺序、数量以及使用关键字实参的方式也可能导致错误，从而增加了理解的\u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m复杂性。\u001b[0m\u001b[32m                                                                                                            \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">╭──────────────────────────╮\n", "│ <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">🧠 Memory 观测 - Round 1</span> │\n", "╰──────────────────────────╯\n", "</pre>\n"], "text/plain": ["╭──────────────────────────╮\n", "│ \u001b[1;35m🧠 Memory 观测 - Round 1\u001b[0m │\n", "╰──────────────────────────╯\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-style: italic\">                                            Memory.get() - 最终传给LLM的消息                                            </span>\n", "┏━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃<span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\"> 序号   </span>┃<span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\"> 角色                 </span>┃<span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\"> 内容                                                                                 </span>┃\n", "┡━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\"> 0      </span>│<span style=\"color: #008080; text-decoration-color: #008080\"> MessageRole.SYSTEM   </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;memory&gt;                                                                             </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;course_policy&gt;                                                                      </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 【课程背景】Python                                                                   </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 初学者教程；【助教规则】优先基于检索到的文档回答；无法从文档找到答案时，明确说明不 … </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;/course_policy&gt;                                                                     </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;learner_facts&gt;                                                                      </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;fact&gt;用户对HTML、CSS、JavaScript了解比较多&lt;/fact&gt;                                   </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;fact&gt;用户的目标是拯救全世界&lt;/fact&gt;                                                  </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;fact&gt;用户在经济紧张时寻求建议&lt;/fact&gt;                                                </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;/learner_facts&gt;                                                                     </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;/memory&gt;                                                                            </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\"> 1      </span>│<span style=\"color: #008080; text-decoration-color: #008080\"> MessageRole.USER     </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 如何理解python中所说的参数，感觉是个很难理解的概念呢！                               </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\"> 2      </span>│<span style=\"color: #008080; text-decoration-color: #008080\"> MessageRole.ASSISTA… </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 在Python中，参数是函数定义中用于接收输入信息的变量。参数分为形参和实参。形参是函数 … </span>│\n", "└────────┴──────────────────────┴──────────────────────────────────────────────────────────────────────────────────────┘\n", "</pre>\n"], "text/plain": ["\u001b[3m                                            Memory.get() - 最终传给LLM的消息                                            \u001b[0m\n", "┏━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃\u001b[1;34m \u001b[0m\u001b[1;34m序号  \u001b[0m\u001b[1;34m \u001b[0m┃\u001b[1;34m \u001b[0m\u001b[1;34m角色                \u001b[0m\u001b[1;34m \u001b[0m┃\u001b[1;34m \u001b[0m\u001b[1;34m内容                                                                                \u001b[0m\u001b[1;34m \u001b[0m┃\n", "┡━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│\u001b[2m \u001b[0m\u001b[2m0     \u001b[0m\u001b[2m \u001b[0m│\u001b[36m \u001b[0m\u001b[36mMessageRole.SYSTEM  \u001b[0m\u001b[36m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m<memory>                                                                            \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m<course_policy>                                                                     \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m【课程背景】Python                                                                  \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m初学者教程；【助教规则】优先基于检索到的文档回答；无法从文档找到答案时，明确说明不 …\u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m</course_policy>                                                                    \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m<learner_facts>                                                                     \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m<fact>用户对HTML、CSS、JavaScript了解比较多</fact>                                  \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m<fact>用户的目标是拯救全世界</fact>                                                 \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m<fact>用户在经济紧张时寻求建议</fact>                                               \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m</learner_facts>                                                                    \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m</memory>                                                                           \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m \u001b[0m\u001b[2m1     \u001b[0m\u001b[2m \u001b[0m│\u001b[36m \u001b[0m\u001b[36mMessageRole.USER    \u001b[0m\u001b[36m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m如何理解python中所说的参数，感觉是个很难理解的概念呢！                              \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m \u001b[0m\u001b[2m2     \u001b[0m\u001b[2m \u001b[0m│\u001b[36m \u001b[0m\u001b[36mMessageRole.ASSISTA…\u001b[0m\u001b[36m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m在Python中，参数是函数定义中用于接收输入信息的变量。参数分为形参和实参。形参是函数 …\u001b[0m\u001b[37m \u001b[0m│\n", "└────────┴──────────────────────┴──────────────────────────────────────────────────────────────────────────────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-style: italic\">                                                   Memory Blocks 详情                                                   </span>\n", "┏━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃<span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\"> 块名称               </span>┃<span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\"> 类型                      </span>┃<span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\"> 内容                                                              </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│<span style=\"color: #808000; text-decoration-color: #808000\"> course_policy        </span>│<span style=\"color: #008080; text-decoration-color: #008080\"> StaticMemoryBlock         </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 静态内容:                                                         </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\">                      </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                           </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> [TextBlock(block_type='text', text='【课程背景】Python            </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\">                      </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                           </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 初学者教程；【助教规则】优先基于检索到的文档回答；无法从文档找到… </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\"> learner_facts        </span>│<span style=\"color: #008080; text-decoration-color: #008080\"> FactExtractionMemoryBlock </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 事实数量: 3                                                       </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\">                      </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                           </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> [1] 用户对HTML、CSS、JavaScript了解比较多                         </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\">                      </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                           </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> [2] 用户的目标是拯救全世界                                        </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\">                      </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                           </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> [3] 用户在经济紧张时寻求建议                                      </span>│\n", "└──────────────────────┴───────────────────────────┴───────────────────────────────────────────────────────────────────┘\n", "</pre>\n"], "text/plain": ["\u001b[3m                                                   Memory Blocks 详情                                                   \u001b[0m\n", "┏━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃\u001b[1;32m \u001b[0m\u001b[1;32m块名称              \u001b[0m\u001b[1;32m \u001b[0m┃\u001b[1;32m \u001b[0m\u001b[1;32m类型                     \u001b[0m\u001b[1;32m \u001b[0m┃\u001b[1;32m \u001b[0m\u001b[1;32m内容                                                             \u001b[0m\u001b[1;32m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│\u001b[33m \u001b[0m\u001b[33mcourse_policy       \u001b[0m\u001b[33m \u001b[0m│\u001b[36m \u001b[0m\u001b[36mStaticMemoryBlock        \u001b[0m\u001b[36m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m静态内容:                                                        \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m                      \u001b[0m│\u001b[36m                           \u001b[0m│\u001b[37m \u001b[0m\u001b[37m[TextBlock(block_type='text', text='【课程背景】Python           \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m                      \u001b[0m│\u001b[36m                           \u001b[0m│\u001b[37m \u001b[0m\u001b[37m初学者教程；【助教规则】优先基于检索到的文档回答；无法从文档找到…\u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m \u001b[0m\u001b[33mlearner_facts       \u001b[0m\u001b[33m \u001b[0m│\u001b[36m \u001b[0m\u001b[36mFactExtractionMemoryBlock\u001b[0m\u001b[36m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m事实数量: 3                                                      \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m                      \u001b[0m│\u001b[36m                           \u001b[0m│\u001b[37m \u001b[0m\u001b[37m[1] 用户对HTML、CSS、JavaScript了解比较多                        \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m                      \u001b[0m│\u001b[36m                           \u001b[0m│\u001b[37m \u001b[0m\u001b[37m[2] 用户的目标是拯救全世界                                       \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m                      \u001b[0m│\u001b[36m                           \u001b[0m│\u001b[37m \u001b[0m\u001b[37m[3] 用户在经济紧张时寻求建议                                     \u001b[0m\u001b[37m \u001b[0m│\n", "└──────────────────────┴───────────────────────────┴───────────────────────────────────────────────────────────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-style: italic\">                                                    Memory 统计信息                                                     </span>\n", "┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃<span style=\"color: #800000; text-decoration-color: #800000; font-weight: bold\"> 配置项                          </span>┃<span style=\"color: #800000; text-decoration-color: #800000; font-weight: bold\"> 值                                                                                 </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│<span style=\"color: #808000; text-decoration-color: #808000\"> session_id                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> demo-python-rag                                                                    </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\"> token_limit                     </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 1000                                                                               </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\"> token_flush_size                </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 200                                                                                </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\"> chat_history_token_ratio        </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 0.6                                                                                </span>│\n", "└─────────────────────────────────┴────────────────────────────────────────────────────────────────────────────────────┘\n", "</pre>\n"], "text/plain": ["\u001b[3m                                                    Memory 统计信息                                                     \u001b[0m\n", "┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃\u001b[1;31m \u001b[0m\u001b[1;31m配置项                         \u001b[0m\u001b[1;31m \u001b[0m┃\u001b[1;31m \u001b[0m\u001b[1;31m值                                                                                \u001b[0m\u001b[1;31m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│\u001b[33m \u001b[0m\u001b[33msession_id                     \u001b[0m\u001b[33m \u001b[0m│\u001b[37m \u001b[0m\u001b[37mdemo-python-rag                                                                   \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m \u001b[0m\u001b[33mtoken_limit                    \u001b[0m\u001b[33m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m1000                                                                              \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m \u001b[0m\u001b[33mtoken_flush_size               \u001b[0m\u001b[33m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m200                                                                               \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m \u001b[0m\u001b[33mchat_history_token_ratio       \u001b[0m\u001b[33m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m0.6                                                                               \u001b[0m\u001b[37m \u001b[0m│\n", "└─────────────────────────────────┴────────────────────────────────────────────────────────────────────────────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">========================================================================================================================\n", "</pre>\n"], "text/plain": ["========================================================================================================================\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">╭──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">│ 🗣️ [Round 2 - 记录事实] 个人背景信息                                                                                  │</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;34m╭──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\u001b[0m\n", "\u001b[1;34m│\u001b[0m\u001b[1;34m \u001b[0m\u001b[1;34m🗣️ [Round 2 - 记录事实] 个人背景信息\u001b[0m\u001b[1;34m                                                                                 \u001b[0m\u001b[1;34m \u001b[0m\u001b[1;34m│\u001b[0m\n", "\u001b[1;34m╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Querying with: 在Python中，如何理解参数的概念，特别是对于我这样对HTML、CSS和JavaScript了解较多的人来说？\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">╭──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│ 🧠 答：                                                                                                              │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│ 在Python中，参数的概念可以通过形参和实参来理解。形参是函数定义中用于接收输入的变量，而实参是在调用函数时传递给这些形 │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│ 参的具体值。例如，在一个函数定义中，形参可能是`username`，而在调用该函数时，传递的值如`'jesse'`就是实参。            │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│                                                                                                                      │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│ 对于熟悉HTML、CSS和JavaScript的人来说，可以将形参视为函数的“输入框”，而实参则是实际填入这些输入框的值。理解参数的使  │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│ 用方式有助于编写更清晰和可重用的代码。在Python中，参数可以通过位置实参、关键字实参等多种方式传递，这使得函数调用更加 │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│ 灵活和易于理解。                                                                                                     │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[32m╭──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m🧠 答：\u001b[0m\u001b[32m                                                                                                             \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m在Python中，参数的概念可以通过形参和实参来理解。形参是函数定义中用于接收输入的变量，而实参是在调用函数时传递给这些形\u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m参的具体值。例如，在一个函数定义中，形参可能是`username`，而在调用该函数时，传递的值如`'jesse'`就是实参。\u001b[0m\u001b[32m           \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m                                                                                                                    \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m对于熟悉HTML、CSS和JavaScript的人来说，可以将形参视为函数的“输入框”，而实参则是实际填入这些输入框的值。理解参数的使\u001b[0m\u001b[32m \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m用方式有助于编写更清晰和可重用的代码。在Python中，参数可以通过位置实参、关键字实参等多种方式传递，这使得函数调用更加\u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m灵活和易于理解。\u001b[0m\u001b[32m                                                                                                    \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">╭──────────────────────────╮\n", "│ <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">🧠 Memory 观测 - Round 2</span> │\n", "╰──────────────────────────╯\n", "</pre>\n"], "text/plain": ["╭──────────────────────────╮\n", "│ \u001b[1;35m🧠 Memory 观测 - Round 2\u001b[0m │\n", "╰──────────────────────────╯\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-style: italic\">                                            Memory.get() - 最终传给LLM的消息                                            </span>\n", "┏━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃<span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\"> 序号   </span>┃<span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\"> 角色                 </span>┃<span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\"> 内容                                                                                 </span>┃\n", "┡━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\"> 0      </span>│<span style=\"color: #008080; text-decoration-color: #008080\"> MessageRole.SYSTEM   </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;memory&gt;                                                                             </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;course_policy&gt;                                                                      </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 【课程背景】Python                                                                   </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 初学者教程；【助教规则】优先基于检索到的文档回答；无法从文档找到答案时，明确说明不 … </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;/course_policy&gt;                                                                     </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;learner_facts&gt;                                                                      </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;fact&gt;用户对HTML、CSS、JavaScript了解比较多&lt;/fact&gt;                                   </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;fact&gt;用户的目标是拯救全世界&lt;/fact&gt;                                                  </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;fact&gt;用户在经济紧张时寻求建议&lt;/fact&gt;                                                </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;/learner_facts&gt;                                                                     </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;/memory&gt;                                                                            </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\"> 1      </span>│<span style=\"color: #008080; text-decoration-color: #008080\"> MessageRole.USER     </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 如何理解python中所说的参数，感觉是个很难理解的概念呢！                               </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\"> 2      </span>│<span style=\"color: #008080; text-decoration-color: #008080\"> MessageRole.ASSISTA… </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 在Python中，参数是函数定义中用于接收输入信息的变量。参数分为形参和实参。形参是函数 … </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\"> 3      </span>│<span style=\"color: #008080; text-decoration-color: #008080\"> MessageRole.USER     </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 我对html、css、js了解比较多，还有，我的目标是拯救全世界，你要记住。                  </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\"> 4      </span>│<span style=\"color: #008080; text-decoration-color: #008080\"> MessageRole.ASSISTA… </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 在Python中，参数的概念可以通过形参和实参来理解。形参是函数定义中用于接收输入的变量 … </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">                                                                                      </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 对于熟悉HTML、CSS和JavaScript的人来说，可以将形参视为函数的“输入框”，而实参则是实际… </span>│\n", "└────────┴──────────────────────┴──────────────────────────────────────────────────────────────────────────────────────┘\n", "</pre>\n"], "text/plain": ["\u001b[3m                                            Memory.get() - 最终传给LLM的消息                                            \u001b[0m\n", "┏━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃\u001b[1;34m \u001b[0m\u001b[1;34m序号  \u001b[0m\u001b[1;34m \u001b[0m┃\u001b[1;34m \u001b[0m\u001b[1;34m角色                \u001b[0m\u001b[1;34m \u001b[0m┃\u001b[1;34m \u001b[0m\u001b[1;34m内容                                                                                \u001b[0m\u001b[1;34m \u001b[0m┃\n", "┡━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│\u001b[2m \u001b[0m\u001b[2m0     \u001b[0m\u001b[2m \u001b[0m│\u001b[36m \u001b[0m\u001b[36mMessageRole.SYSTEM  \u001b[0m\u001b[36m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m<memory>                                                                            \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m<course_policy>                                                                     \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m【课程背景】Python                                                                  \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m初学者教程；【助教规则】优先基于检索到的文档回答；无法从文档找到答案时，明确说明不 …\u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m</course_policy>                                                                    \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m<learner_facts>                                                                     \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m<fact>用户对HTML、CSS、JavaScript了解比较多</fact>                                  \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m<fact>用户的目标是拯救全世界</fact>                                                 \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m<fact>用户在经济紧张时寻求建议</fact>                                               \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m</learner_facts>                                                                    \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m</memory>                                                                           \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m \u001b[0m\u001b[2m1     \u001b[0m\u001b[2m \u001b[0m│\u001b[36m \u001b[0m\u001b[36mMessageRole.USER    \u001b[0m\u001b[36m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m如何理解python中所说的参数，感觉是个很难理解的概念呢！                              \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m \u001b[0m\u001b[2m2     \u001b[0m\u001b[2m \u001b[0m│\u001b[36m \u001b[0m\u001b[36mMessageRole.ASSISTA…\u001b[0m\u001b[36m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m在Python中，参数是函数定义中用于接收输入信息的变量。参数分为形参和实参。形参是函数 …\u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m \u001b[0m\u001b[2m3     \u001b[0m\u001b[2m \u001b[0m│\u001b[36m \u001b[0m\u001b[36mMessageRole.USER    \u001b[0m\u001b[36m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m我对html、css、js了解比较多，还有，我的目标是拯救全世界，你要记住。                 \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m \u001b[0m\u001b[2m4     \u001b[0m\u001b[2m \u001b[0m│\u001b[36m \u001b[0m\u001b[36mMessageRole.ASSISTA…\u001b[0m\u001b[36m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m在Python中，参数的概念可以通过形参和实参来理解。形参是函数定义中用于接收输入的变量 …\u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m                                                                                    \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m对于熟悉HTML、CSS和JavaScript的人来说，可以将形参视为函数的“输入框”，而实参则是实际…\u001b[0m\u001b[37m \u001b[0m│\n", "└────────┴──────────────────────┴──────────────────────────────────────────────────────────────────────────────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-style: italic\">                                                   Memory Blocks 详情                                                   </span>\n", "┏━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃<span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\"> 块名称               </span>┃<span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\"> 类型                      </span>┃<span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\"> 内容                                                              </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│<span style=\"color: #808000; text-decoration-color: #808000\"> course_policy        </span>│<span style=\"color: #008080; text-decoration-color: #008080\"> StaticMemoryBlock         </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 静态内容:                                                         </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\">                      </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                           </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> [TextBlock(block_type='text', text='【课程背景】Python            </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\">                      </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                           </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 初学者教程；【助教规则】优先基于检索到的文档回答；无法从文档找到… </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\"> learner_facts        </span>│<span style=\"color: #008080; text-decoration-color: #008080\"> FactExtractionMemoryBlock </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 事实数量: 3                                                       </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\">                      </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                           </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> [1] 用户对HTML、CSS、JavaScript了解比较多                         </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\">                      </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                           </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> [2] 用户的目标是拯救全世界                                        </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\">                      </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                           </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> [3] 用户在经济紧张时寻求建议                                      </span>│\n", "└──────────────────────┴───────────────────────────┴───────────────────────────────────────────────────────────────────┘\n", "</pre>\n"], "text/plain": ["\u001b[3m                                                   Memory Blocks 详情                                                   \u001b[0m\n", "┏━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃\u001b[1;32m \u001b[0m\u001b[1;32m块名称              \u001b[0m\u001b[1;32m \u001b[0m┃\u001b[1;32m \u001b[0m\u001b[1;32m类型                     \u001b[0m\u001b[1;32m \u001b[0m┃\u001b[1;32m \u001b[0m\u001b[1;32m内容                                                             \u001b[0m\u001b[1;32m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│\u001b[33m \u001b[0m\u001b[33mcourse_policy       \u001b[0m\u001b[33m \u001b[0m│\u001b[36m \u001b[0m\u001b[36mStaticMemoryBlock        \u001b[0m\u001b[36m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m静态内容:                                                        \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m                      \u001b[0m│\u001b[36m                           \u001b[0m│\u001b[37m \u001b[0m\u001b[37m[TextBlock(block_type='text', text='【课程背景】Python           \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m                      \u001b[0m│\u001b[36m                           \u001b[0m│\u001b[37m \u001b[0m\u001b[37m初学者教程；【助教规则】优先基于检索到的文档回答；无法从文档找到…\u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m \u001b[0m\u001b[33mlearner_facts       \u001b[0m\u001b[33m \u001b[0m│\u001b[36m \u001b[0m\u001b[36mFactExtractionMemoryBlock\u001b[0m\u001b[36m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m事实数量: 3                                                      \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m                      \u001b[0m│\u001b[36m                           \u001b[0m│\u001b[37m \u001b[0m\u001b[37m[1] 用户对HTML、CSS、JavaScript了解比较多                        \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m                      \u001b[0m│\u001b[36m                           \u001b[0m│\u001b[37m \u001b[0m\u001b[37m[2] 用户的目标是拯救全世界                                       \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m                      \u001b[0m│\u001b[36m                           \u001b[0m│\u001b[37m \u001b[0m\u001b[37m[3] 用户在经济紧张时寻求建议                                     \u001b[0m\u001b[37m \u001b[0m│\n", "└──────────────────────┴───────────────────────────┴───────────────────────────────────────────────────────────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-style: italic\">                                                    Memory 统计信息                                                     </span>\n", "┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃<span style=\"color: #800000; text-decoration-color: #800000; font-weight: bold\"> 配置项                          </span>┃<span style=\"color: #800000; text-decoration-color: #800000; font-weight: bold\"> 值                                                                                 </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│<span style=\"color: #808000; text-decoration-color: #808000\"> session_id                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> demo-python-rag                                                                    </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\"> token_limit                     </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 1000                                                                               </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\"> token_flush_size                </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 200                                                                                </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\"> chat_history_token_ratio        </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 0.6                                                                                </span>│\n", "└─────────────────────────────────┴────────────────────────────────────────────────────────────────────────────────────┘\n", "</pre>\n"], "text/plain": ["\u001b[3m                                                    Memory 统计信息                                                     \u001b[0m\n", "┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃\u001b[1;31m \u001b[0m\u001b[1;31m配置项                         \u001b[0m\u001b[1;31m \u001b[0m┃\u001b[1;31m \u001b[0m\u001b[1;31m值                                                                                \u001b[0m\u001b[1;31m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│\u001b[33m \u001b[0m\u001b[33msession_id                     \u001b[0m\u001b[33m \u001b[0m│\u001b[37m \u001b[0m\u001b[37mdemo-python-rag                                                                   \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m \u001b[0m\u001b[33mtoken_limit                    \u001b[0m\u001b[33m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m1000                                                                              \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m \u001b[0m\u001b[33mtoken_flush_size               \u001b[0m\u001b[33m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m200                                                                               \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m \u001b[0m\u001b[33mchat_history_token_ratio       \u001b[0m\u001b[33m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m0.6                                                                               \u001b[0m\u001b[37m \u001b[0m│\n", "└─────────────────────────────────┴────────────────────────────────────────────────────────────────────────────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">========================================================================================================================\n", "</pre>\n"], "text/plain": ["========================================================================================================================\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">╭──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">│ 🗣️ [Round 2.5 - 追加事实] 教学风格偏好                                                                                │</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;34m╭──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\u001b[0m\n", "\u001b[1;34m│\u001b[0m\u001b[1;34m \u001b[0m\u001b[1;34m🗣️ [Round 2.5 - 追加事实] 教学风格偏好\u001b[0m\u001b[1;34m                                                                               \u001b[0m\u001b[1;34m \u001b[0m\u001b[1;34m│\u001b[0m\n", "\u001b[1;34m╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Querying with: 你能用理查德·费曼的风格来解释一下Python中的参数概念吗？\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">╭──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│ 🧠 答：                                                                                                              │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│ 想象一下，你在组织一个聚会，邀请了几位朋友。每位朋友都有不同的需求，比如饮料、食物和座位。为了确保每个人都能享受到聚 │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│ 会的乐趣，你需要提前知道他们的偏好。这就像在编写一个函数时，你需要定义一些“形参”，这些形参就像是你为每位朋友准备的需 │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│ 求清单。                                                                                                             │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│                                                                                                                      │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│ 当你实际邀请朋友时，你会告诉他们你准备了什么，比如“我为你准备了可乐和比萨”。这时，你所提供的具体信息就是“实参”。在函 │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│ 数中，形参是你在定义函数时所设定的，而实参则是在调用函数时传递给它的具体值。                                         │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│                                                                                                                      │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│ 如果你有多个朋友（形参），你可以选择按顺序告诉他们你准备了什么（位置实参），或者直接告诉他们各自的需求（关键字实参） │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│ 。这样，无论你是如何传递信息，Python都能理解并将这些信息与相应的形参关联起来。                                       │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│                                                                                                                      │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│ 总之，形参和实参的概念就像是你为聚会准备的需求清单和你实际提供的具体信息。通过这种方式，你可以确保每位朋友都能在聚会 │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│ 上享受到他们想要的东西，而你的聚会也会因此更加成功。                                                                 │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[32m╭──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m🧠 答：\u001b[0m\u001b[32m                                                                                                             \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m想象一下，你在组织一个聚会，邀请了几位朋友。每位朋友都有不同的需求，比如饮料、食物和座位。为了确保每个人都能享受到聚\u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m会的乐趣，你需要提前知道他们的偏好。这就像在编写一个函数时，你需要定义一些“形参”，这些形参就像是你为每位朋友准备的需\u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m求清单。\u001b[0m\u001b[32m                                                                                                            \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m                                                                                                                    \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m当你实际邀请朋友时，你会告诉他们你准备了什么，比如“我为你准备了可乐和比萨”。这时，你所提供的具体信息就是“实参”。在函\u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m数中，形参是你在定义函数时所设定的，而实参则是在调用函数时传递给它的具体值。\u001b[0m\u001b[32m                                        \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m                                                                                                                    \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m如果你有多个朋友（形参），你可以选择按顺序告诉他们你准备了什么（位置实参），或者直接告诉他们各自的需求（关键字实参）\u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m。这样，无论你是如何传递信息，Python都能理解并将这些信息与相应的形参关联起来。\u001b[0m\u001b[32m                                      \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m                                                                                                                    \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m总之，形参和实参的概念就像是你为聚会准备的需求清单和你实际提供的具体信息。通过这种方式，你可以确保每位朋友都能在聚会\u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m上享受到他们想要的东西，而你的聚会也会因此更加成功。\u001b[0m\u001b[32m                                                                \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">╭────────────────────────────╮\n", "│ <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">🧠 Memory 观测 - Round 2.5</span> │\n", "╰────────────────────────────╯\n", "</pre>\n"], "text/plain": ["╭────────────────────────────╮\n", "│ \u001b[1;35m🧠 Memory 观测 - Round 2.5\u001b[0m │\n", "╰────────────────────────────╯\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-style: italic\">                                            Memory.get() - 最终传给LLM的消息                                            </span>\n", "┏━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃<span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\"> 序号   </span>┃<span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\"> 角色                 </span>┃<span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\"> 内容                                                                                 </span>┃\n", "┡━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\"> 0      </span>│<span style=\"color: #008080; text-decoration-color: #008080\"> MessageRole.SYSTEM   </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;memory&gt;                                                                             </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;course_policy&gt;                                                                      </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 【课程背景】Python                                                                   </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 初学者教程；【助教规则】优先基于检索到的文档回答；无法从文档找到答案时，明确说明不 … </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;/course_policy&gt;                                                                     </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;learner_facts&gt;                                                                      </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;fact&gt;用户对HTML、CSS、JavaScript了解比较多&lt;/fact&gt;                                   </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;fact&gt;用户的目标是拯救全世界&lt;/fact&gt;                                                  </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;fact&gt;用户在经济紧张时寻求建议&lt;/fact&gt;                                                </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;/learner_facts&gt;                                                                     </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;/memory&gt;                                                                            </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\"> 1      </span>│<span style=\"color: #008080; text-decoration-color: #008080\"> MessageRole.USER     </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 你在跟我讲解的时候要用理查德·费曼的风格，你要记住。                                  </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\"> 2      </span>│<span style=\"color: #008080; text-decoration-color: #008080\"> MessageRole.ASSISTA… </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 想象一下，你在组织一个聚会，邀请了几位朋友。每位朋友都有不同的需求，比如饮料、食物 … </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">                                                                                      </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 当你实际邀请朋友时，你会告诉他们你准备了什么，比如“我为你准备了可乐和比萨”。这时， … </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">                                                                                      </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 如果你有多个朋友（形参），你可以选择按顺序告诉他们你准备了什么（位置实参），或者直 … </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">                                                                                      </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 总之，形参和实参的概念就像是你为聚会准备的需求清单和你实际提供的具体信息。通过这种 … </span>│\n", "└────────┴──────────────────────┴──────────────────────────────────────────────────────────────────────────────────────┘\n", "</pre>\n"], "text/plain": ["\u001b[3m                                            Memory.get() - 最终传给LLM的消息                                            \u001b[0m\n", "┏━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃\u001b[1;34m \u001b[0m\u001b[1;34m序号  \u001b[0m\u001b[1;34m \u001b[0m┃\u001b[1;34m \u001b[0m\u001b[1;34m角色                \u001b[0m\u001b[1;34m \u001b[0m┃\u001b[1;34m \u001b[0m\u001b[1;34m内容                                                                                \u001b[0m\u001b[1;34m \u001b[0m┃\n", "┡━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│\u001b[2m \u001b[0m\u001b[2m0     \u001b[0m\u001b[2m \u001b[0m│\u001b[36m \u001b[0m\u001b[36mMessageRole.SYSTEM  \u001b[0m\u001b[36m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m<memory>                                                                            \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m<course_policy>                                                                     \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m【课程背景】Python                                                                  \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m初学者教程；【助教规则】优先基于检索到的文档回答；无法从文档找到答案时，明确说明不 …\u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m</course_policy>                                                                    \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m<learner_facts>                                                                     \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m<fact>用户对HTML、CSS、JavaScript了解比较多</fact>                                  \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m<fact>用户的目标是拯救全世界</fact>                                                 \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m<fact>用户在经济紧张时寻求建议</fact>                                               \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m</learner_facts>                                                                    \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m</memory>                                                                           \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m \u001b[0m\u001b[2m1     \u001b[0m\u001b[2m \u001b[0m│\u001b[36m \u001b[0m\u001b[36mMessageRole.USER    \u001b[0m\u001b[36m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m你在跟我讲解的时候要用理查德·费曼的风格，你要记住。                                 \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m \u001b[0m\u001b[2m2     \u001b[0m\u001b[2m \u001b[0m│\u001b[36m \u001b[0m\u001b[36mMessageRole.ASSISTA…\u001b[0m\u001b[36m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m想象一下，你在组织一个聚会，邀请了几位朋友。每位朋友都有不同的需求，比如饮料、食物 …\u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m                                                                                    \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m当你实际邀请朋友时，你会告诉他们你准备了什么，比如“我为你准备了可乐和比萨”。这时， …\u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m                                                                                    \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m如果你有多个朋友（形参），你可以选择按顺序告诉他们你准备了什么（位置实参），或者直 …\u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m                                                                                    \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m总之，形参和实参的概念就像是你为聚会准备的需求清单和你实际提供的具体信息。通过这种 …\u001b[0m\u001b[37m \u001b[0m│\n", "└────────┴──────────────────────┴──────────────────────────────────────────────────────────────────────────────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-style: italic\">                                                   Memory Blocks 详情                                                   </span>\n", "┏━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃<span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\"> 块名称               </span>┃<span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\"> 类型                      </span>┃<span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\"> 内容                                                              </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│<span style=\"color: #808000; text-decoration-color: #808000\"> course_policy        </span>│<span style=\"color: #008080; text-decoration-color: #008080\"> StaticMemoryBlock         </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 静态内容:                                                         </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\">                      </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                           </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> [TextBlock(block_type='text', text='【课程背景】Python            </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\">                      </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                           </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 初学者教程；【助教规则】优先基于检索到的文档回答；无法从文档找到… </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\"> learner_facts        </span>│<span style=\"color: #008080; text-decoration-color: #008080\"> FactExtractionMemoryBlock </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 事实数量: 3                                                       </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\">                      </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                           </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> [1] 用户对HTML、CSS、JavaScript了解比较多                         </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\">                      </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                           </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> [2] 用户的目标是拯救全世界                                        </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\">                      </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                           </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> [3] 用户在经济紧张时寻求建议                                      </span>│\n", "└──────────────────────┴───────────────────────────┴───────────────────────────────────────────────────────────────────┘\n", "</pre>\n"], "text/plain": ["\u001b[3m                                                   Memory Blocks 详情                                                   \u001b[0m\n", "┏━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃\u001b[1;32m \u001b[0m\u001b[1;32m块名称              \u001b[0m\u001b[1;32m \u001b[0m┃\u001b[1;32m \u001b[0m\u001b[1;32m类型                     \u001b[0m\u001b[1;32m \u001b[0m┃\u001b[1;32m \u001b[0m\u001b[1;32m内容                                                             \u001b[0m\u001b[1;32m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│\u001b[33m \u001b[0m\u001b[33mcourse_policy       \u001b[0m\u001b[33m \u001b[0m│\u001b[36m \u001b[0m\u001b[36mStaticMemoryBlock        \u001b[0m\u001b[36m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m静态内容:                                                        \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m                      \u001b[0m│\u001b[36m                           \u001b[0m│\u001b[37m \u001b[0m\u001b[37m[TextBlock(block_type='text', text='【课程背景】Python           \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m                      \u001b[0m│\u001b[36m                           \u001b[0m│\u001b[37m \u001b[0m\u001b[37m初学者教程；【助教规则】优先基于检索到的文档回答；无法从文档找到…\u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m \u001b[0m\u001b[33mlearner_facts       \u001b[0m\u001b[33m \u001b[0m│\u001b[36m \u001b[0m\u001b[36mFactExtractionMemoryBlock\u001b[0m\u001b[36m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m事实数量: 3                                                      \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m                      \u001b[0m│\u001b[36m                           \u001b[0m│\u001b[37m \u001b[0m\u001b[37m[1] 用户对HTML、CSS、JavaScript了解比较多                        \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m                      \u001b[0m│\u001b[36m                           \u001b[0m│\u001b[37m \u001b[0m\u001b[37m[2] 用户的目标是拯救全世界                                       \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m                      \u001b[0m│\u001b[36m                           \u001b[0m│\u001b[37m \u001b[0m\u001b[37m[3] 用户在经济紧张时寻求建议                                     \u001b[0m\u001b[37m \u001b[0m│\n", "└──────────────────────┴───────────────────────────┴───────────────────────────────────────────────────────────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-style: italic\">                                                    Memory 统计信息                                                     </span>\n", "┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃<span style=\"color: #800000; text-decoration-color: #800000; font-weight: bold\"> 配置项                          </span>┃<span style=\"color: #800000; text-decoration-color: #800000; font-weight: bold\"> 值                                                                                 </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│<span style=\"color: #808000; text-decoration-color: #808000\"> session_id                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> demo-python-rag                                                                    </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\"> token_limit                     </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 1000                                                                               </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\"> token_flush_size                </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 200                                                                                </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\"> chat_history_token_ratio        </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 0.6                                                                                </span>│\n", "└─────────────────────────────────┴────────────────────────────────────────────────────────────────────────────────────┘\n", "</pre>\n"], "text/plain": ["\u001b[3m                                                    Memory 统计信息                                                     \u001b[0m\n", "┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃\u001b[1;31m \u001b[0m\u001b[1;31m配置项                         \u001b[0m\u001b[1;31m \u001b[0m┃\u001b[1;31m \u001b[0m\u001b[1;31m值                                                                                \u001b[0m\u001b[1;31m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│\u001b[33m \u001b[0m\u001b[33msession_id                     \u001b[0m\u001b[33m \u001b[0m│\u001b[37m \u001b[0m\u001b[37mdemo-python-rag                                                                   \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m \u001b[0m\u001b[33mtoken_limit                    \u001b[0m\u001b[33m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m1000                                                                              \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m \u001b[0m\u001b[33mtoken_flush_size               \u001b[0m\u001b[33m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m200                                                                               \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m \u001b[0m\u001b[33mchat_history_token_ratio       \u001b[0m\u001b[33m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m0.6                                                                               \u001b[0m\u001b[37m \u001b[0m│\n", "└─────────────────────────────────┴────────────────────────────────────────────────────────────────────────────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">========================================================================================================================\n", "</pre>\n"], "text/plain": ["========================================================================================================================\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">╭──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">│ 🗣️ [Round 3 - 无检索，仅用记忆] 生活问题                                                                              │</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;34m╭──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\u001b[0m\n", "\u001b[1;34m│\u001b[0m\u001b[1;34m \u001b[0m\u001b[1;34m🗣️ [Round 3 - 无检索，仅用记忆] 生活问题\u001b[0m\u001b[1;34m                                                                             \u001b[0m\u001b[1;34m \u001b[0m\u001b[1;34m│\u001b[0m\n", "\u001b[1;34m╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">╭──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│ 💬 simple 答：                                                                                                       │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│ 在经济紧张时，可以考虑以下几种策略：                                                                                 │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│                                                                                                                      │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│ 1. **制定预算**：                                                                                                    │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│    - 记录收入和支出，找出可削减的开支。                                                                              │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│    - 优先考虑必要的支出，如食物和住房。                                                                              │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│                                                                                                                      │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│ 2. **寻找额外收入**：                                                                                                │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│    - 考虑兼职工作或自由职业，利用自己的技能。                                                                        │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│    - 出售不再需要的物品，获取一些现金。                                                                              │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│                                                                                                                      │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│ 3. **利用资源**：                                                                                                    │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│    - 寻找社区资源，如食品银行或社会服务机构。                                                                        │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│    - 询问朋友和家人是否可以提供短期帮助。                                                                            │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│                                                                                                                      │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│ 4. **学习理财知识**：                                                                                                │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│    - 阅读相关书籍或参加在线课程，提升自己的财务管理能力。                                                            │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│    - 了解如何投资和储蓄，以便未来更好地应对经济压力。                                                                │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│                                                                                                                      │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│ 5. **保持积极心态**：                                                                                                │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│    - 设定小目标，逐步改善经济状况。                                                                                  │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│    - 与他人分享你的经历，获得支持和建议。                                                                            │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│                                                                                                                      │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│ 这些方法可以帮助你在经济紧张时更好地应对挑战。                                                                       │</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[32m╭──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m💬 simple 答：\u001b[0m\u001b[32m                                                                                                      \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m在经济紧张时，可以考虑以下几种策略：\u001b[0m\u001b[32m                                                                                \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m                                                                                                                    \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m1. **制定预算**：\u001b[0m\u001b[32m                                                                                                   \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m   - 记录收入和支出，找出可削减的开支。\u001b[0m\u001b[32m                                                                             \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m   - 优先考虑必要的支出，如食物和住房。\u001b[0m\u001b[32m                                                                             \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m                                                                                                                    \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m2. **寻找额外收入**：\u001b[0m\u001b[32m                                                                                               \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m   - 考虑兼职工作或自由职业，利用自己的技能。\u001b[0m\u001b[32m                                                                       \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m   - 出售不再需要的物品，获取一些现金。\u001b[0m\u001b[32m                                                                             \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m                                                                                                                    \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m3. **利用资源**：\u001b[0m\u001b[32m                                                                                                   \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m   - 寻找社区资源，如食品银行或社会服务机构。\u001b[0m\u001b[32m                                                                       \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m   - 询问朋友和家人是否可以提供短期帮助。\u001b[0m\u001b[32m                                                                           \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m                                                                                                                    \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m4. **学习理财知识**：\u001b[0m\u001b[32m                                                                                               \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m   - 阅读相关书籍或参加在线课程，提升自己的财务管理能力。\u001b[0m\u001b[32m                                                           \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m   - 了解如何投资和储蓄，以便未来更好地应对经济压力。\u001b[0m\u001b[32m                                                               \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m                                                                                                                    \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m5. **保持积极心态**：\u001b[0m\u001b[32m                                                                                               \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m   - 设定小目标，逐步改善经济状况。\u001b[0m\u001b[32m                                                                                 \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m   - 与他人分享你的经历，获得支持和建议。\u001b[0m\u001b[32m                                                                           \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m                                                                                                                    \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m\u001b[32m \u001b[0m\u001b[32m这些方法可以帮助你在经济紧张时更好地应对挑战。\u001b[0m\u001b[32m                                                                      \u001b[0m\u001b[32m \u001b[0m\u001b[32m│\u001b[0m\n", "\u001b[32m╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">╭──────────────────────────╮\n", "│ <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">🧠 Memory 观测 - Round 3</span> │\n", "╰──────────────────────────╯\n", "</pre>\n"], "text/plain": ["╭──────────────────────────╮\n", "│ \u001b[1;35m🧠 Memory 观测 - Round 3\u001b[0m │\n", "╰──────────────────────────╯\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-style: italic\">                                            Memory.get() - 最终传给LLM的消息                                            </span>\n", "┏━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃<span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\"> 序号   </span>┃<span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\"> 角色                 </span>┃<span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\"> 内容                                                                                 </span>┃\n", "┡━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\"> 0      </span>│<span style=\"color: #008080; text-decoration-color: #008080\"> MessageRole.SYSTEM   </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;memory&gt;                                                                             </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;course_policy&gt;                                                                      </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 【课程背景】Python                                                                   </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 初学者教程；【助教规则】优先基于检索到的文档回答；无法从文档找到答案时，明确说明不 … </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;/course_policy&gt;                                                                     </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;learner_facts&gt;                                                                      </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;fact&gt;用户对HTML、CSS、JavaScript了解比较多&lt;/fact&gt;                                   </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;fact&gt;用户的目标是拯救全世界&lt;/fact&gt;                                                  </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;fact&gt;用户在经济紧张时寻求建议&lt;/fact&gt;                                                </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;/learner_facts&gt;                                                                     </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> &lt;/memory&gt;                                                                            </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\"> 1      </span>│<span style=\"color: #008080; text-decoration-color: #008080\"> MessageRole.USER     </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 说说没钱的时候应该怎么办？                                                           </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\"> 2      </span>│<span style=\"color: #008080; text-decoration-color: #008080\"> MessageRole.ASSISTA… </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 在经济紧张时，可以考虑以下几种策略：                                                 </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">                                                                                      </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 1. **制定预算**：                                                                    </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    - 记录收入和支出，找出可削减的开支。                                              </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    - 优先考虑必要的支出，如食物和住房。                                              </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">                                                                                      </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 2. **寻找额外收入**：                                                                </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    - 考虑兼职工作或自由职业，利用自己的技能。                                        </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    - 出售不再需要的物品，获取一些现金。                                              </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">                                                                                      </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 3. **利用资源**：                                                                    </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    - 寻找社区资源，如食品银行或社会服务机构。                                        </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    - 询问朋友和家人是否可以提供短期帮助。                                            </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">                                                                                      </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 4. **学习理财知识**：                                                                </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    - 阅读相关书籍或参加在线课程，提升自己的财务管理能力。                            </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    - 了解如何投资和储蓄，以便未来更好地应对经济压力。                                </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">                                                                                      </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 5. **保持积极心态**：                                                                </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    - 设定小目标，逐步改善经济状况。                                                  </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    - 与他人分享你的经历，获得支持和建议。                                            </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">                                                                                      </span>│\n", "│<span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">        </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 这些方法可以帮助你在经济紧张时更好地应对挑战。                                       </span>│\n", "└────────┴──────────────────────┴──────────────────────────────────────────────────────────────────────────────────────┘\n", "</pre>\n"], "text/plain": ["\u001b[3m                                            Memory.get() - 最终传给LLM的消息                                            \u001b[0m\n", "┏━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃\u001b[1;34m \u001b[0m\u001b[1;34m序号  \u001b[0m\u001b[1;34m \u001b[0m┃\u001b[1;34m \u001b[0m\u001b[1;34m角色                \u001b[0m\u001b[1;34m \u001b[0m┃\u001b[1;34m \u001b[0m\u001b[1;34m内容                                                                                \u001b[0m\u001b[1;34m \u001b[0m┃\n", "┡━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│\u001b[2m \u001b[0m\u001b[2m0     \u001b[0m\u001b[2m \u001b[0m│\u001b[36m \u001b[0m\u001b[36mMessageRole.SYSTEM  \u001b[0m\u001b[36m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m<memory>                                                                            \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m<course_policy>                                                                     \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m【课程背景】Python                                                                  \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m初学者教程；【助教规则】优先基于检索到的文档回答；无法从文档找到答案时，明确说明不 …\u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m</course_policy>                                                                    \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m<learner_facts>                                                                     \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m<fact>用户对HTML、CSS、JavaScript了解比较多</fact>                                  \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m<fact>用户的目标是拯救全世界</fact>                                                 \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m<fact>用户在经济紧张时寻求建议</fact>                                               \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m</learner_facts>                                                                    \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m</memory>                                                                           \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m \u001b[0m\u001b[2m1     \u001b[0m\u001b[2m \u001b[0m│\u001b[36m \u001b[0m\u001b[36mMessageRole.USER    \u001b[0m\u001b[36m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m说说没钱的时候应该怎么办？                                                          \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m \u001b[0m\u001b[2m2     \u001b[0m\u001b[2m \u001b[0m│\u001b[36m \u001b[0m\u001b[36mMessageRole.ASSISTA…\u001b[0m\u001b[36m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m在经济紧张时，可以考虑以下几种策略：                                                \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m                                                                                    \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m1. **制定预算**：                                                                   \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m   - 记录收入和支出，找出可削减的开支。                                             \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m   - 优先考虑必要的支出，如食物和住房。                                             \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m                                                                                    \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m2. **寻找额外收入**：                                                               \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m   - 考虑兼职工作或自由职业，利用自己的技能。                                       \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m   - 出售不再需要的物品，获取一些现金。                                             \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m                                                                                    \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m3. **利用资源**：                                                                   \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m   - 寻找社区资源，如食品银行或社会服务机构。                                       \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m   - 询问朋友和家人是否可以提供短期帮助。                                           \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m                                                                                    \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m4. **学习理财知识**：                                                               \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m   - 阅读相关书籍或参加在线课程，提升自己的财务管理能力。                           \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m   - 了解如何投资和储蓄，以便未来更好地应对经济压力。                               \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m                                                                                    \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m5. **保持积极心态**：                                                               \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m   - 设定小目标，逐步改善经济状况。                                                 \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m   - 与他人分享你的经历，获得支持和建议。                                           \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m                                                                                    \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[2m        \u001b[0m│\u001b[36m                      \u001b[0m│\u001b[37m \u001b[0m\u001b[37m这些方法可以帮助你在经济紧张时更好地应对挑战。                                      \u001b[0m\u001b[37m \u001b[0m│\n", "└────────┴──────────────────────┴──────────────────────────────────────────────────────────────────────────────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-style: italic\">                                                   Memory Blocks 详情                                                   </span>\n", "┏━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃<span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\"> 块名称               </span>┃<span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\"> 类型                      </span>┃<span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\"> 内容                                                              </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│<span style=\"color: #808000; text-decoration-color: #808000\"> course_policy        </span>│<span style=\"color: #008080; text-decoration-color: #008080\"> StaticMemoryBlock         </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 静态内容:                                                         </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\">                      </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                           </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> [TextBlock(block_type='text', text='【课程背景】Python            </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\">                      </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                           </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 初学者教程；【助教规则】优先基于检索到的文档回答；无法从文档找到… </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\"> learner_facts        </span>│<span style=\"color: #008080; text-decoration-color: #008080\"> FactExtractionMemoryBlock </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 事实数量: 3                                                       </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\">                      </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                           </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> [1] 用户对HTML、CSS、JavaScript了解比较多                         </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\">                      </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                           </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> [2] 用户的目标是拯救全世界                                        </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\">                      </span>│<span style=\"color: #008080; text-decoration-color: #008080\">                           </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> [3] 用户在经济紧张时寻求建议                                      </span>│\n", "└──────────────────────┴───────────────────────────┴───────────────────────────────────────────────────────────────────┘\n", "</pre>\n"], "text/plain": ["\u001b[3m                                                   Memory Blocks 详情                                                   \u001b[0m\n", "┏━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃\u001b[1;32m \u001b[0m\u001b[1;32m块名称              \u001b[0m\u001b[1;32m \u001b[0m┃\u001b[1;32m \u001b[0m\u001b[1;32m类型                     \u001b[0m\u001b[1;32m \u001b[0m┃\u001b[1;32m \u001b[0m\u001b[1;32m内容                                                             \u001b[0m\u001b[1;32m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│\u001b[33m \u001b[0m\u001b[33mcourse_policy       \u001b[0m\u001b[33m \u001b[0m│\u001b[36m \u001b[0m\u001b[36mStaticMemoryBlock        \u001b[0m\u001b[36m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m静态内容:                                                        \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m                      \u001b[0m│\u001b[36m                           \u001b[0m│\u001b[37m \u001b[0m\u001b[37m[TextBlock(block_type='text', text='【课程背景】Python           \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m                      \u001b[0m│\u001b[36m                           \u001b[0m│\u001b[37m \u001b[0m\u001b[37m初学者教程；【助教规则】优先基于检索到的文档回答；无法从文档找到…\u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m \u001b[0m\u001b[33mlearner_facts       \u001b[0m\u001b[33m \u001b[0m│\u001b[36m \u001b[0m\u001b[36mFactExtractionMemoryBlock\u001b[0m\u001b[36m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m事实数量: 3                                                      \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m                      \u001b[0m│\u001b[36m                           \u001b[0m│\u001b[37m \u001b[0m\u001b[37m[1] 用户对HTML、CSS、JavaScript了解比较多                        \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m                      \u001b[0m│\u001b[36m                           \u001b[0m│\u001b[37m \u001b[0m\u001b[37m[2] 用户的目标是拯救全世界                                       \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m                      \u001b[0m│\u001b[36m                           \u001b[0m│\u001b[37m \u001b[0m\u001b[37m[3] 用户在经济紧张时寻求建议                                     \u001b[0m\u001b[37m \u001b[0m│\n", "└──────────────────────┴───────────────────────────┴───────────────────────────────────────────────────────────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-style: italic\">                                                    Memory 统计信息                                                     </span>\n", "┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃<span style=\"color: #800000; text-decoration-color: #800000; font-weight: bold\"> 配置项                          </span>┃<span style=\"color: #800000; text-decoration-color: #800000; font-weight: bold\"> 值                                                                                 </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│<span style=\"color: #808000; text-decoration-color: #808000\"> session_id                      </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> demo-python-rag                                                                    </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\"> token_limit                     </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 1000                                                                               </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\"> token_flush_size                </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 200                                                                                </span>│\n", "│<span style=\"color: #808000; text-decoration-color: #808000\"> chat_history_token_ratio        </span>│<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\"> 0.6                                                                                </span>│\n", "└─────────────────────────────────┴────────────────────────────────────────────────────────────────────────────────────┘\n", "</pre>\n"], "text/plain": ["\u001b[3m                                                    Memory 统计信息                                                     \u001b[0m\n", "┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃\u001b[1;31m \u001b[0m\u001b[1;31m配置项                         \u001b[0m\u001b[1;31m \u001b[0m┃\u001b[1;31m \u001b[0m\u001b[1;31m值                                                                                \u001b[0m\u001b[1;31m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│\u001b[33m \u001b[0m\u001b[33msession_id                     \u001b[0m\u001b[33m \u001b[0m│\u001b[37m \u001b[0m\u001b[37mdemo-python-rag                                                                   \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m \u001b[0m\u001b[33mtoken_limit                    \u001b[0m\u001b[33m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m1000                                                                              \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m \u001b[0m\u001b[33mtoken_flush_size               \u001b[0m\u001b[33m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m200                                                                               \u001b[0m\u001b[37m \u001b[0m│\n", "│\u001b[33m \u001b[0m\u001b[33mchat_history_token_ratio       \u001b[0m\u001b[33m \u001b[0m│\u001b[37m \u001b[0m\u001b[37m0.6                                                                               \u001b[0m\u001b[37m \u001b[0m│\n", "└─────────────────────────────────┴────────────────────────────────────────────────────────────────────────────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">========================================================================================================================\n", "</pre>\n"], "text/plain": ["========================================================================================================================\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "========================================================================================================================\n", "📋 原始 Memory 内容（无格式化，完整显示）\n", "========================================================================================================================\n", "\n", "🔍 Memory.get() 原始内容:\n", "\n", "[0] Role: MessageRole.SYSTEM\n", "Content: <memory>\n", "<course_policy>\n", "【课程背景】Python 初学者教程；【助教规则】优先基于检索到的文档回答；无法从文档找到答案时，明确说明不知道；回答要举【最小可运行】示例、避免过度抽象；语言：中文；风格：清晰、简短、分点说明。\n", "</course_policy>\n", "<learner_facts>\n", "<fact>用户对HTML、CSS、JavaScript了解比较多</fact>\n", "<fact>用户的目标是拯救全世界</fact>\n", "<fact>用户在经济紧张时寻求建议</fact>\n", "</learner_facts>\n", "</memory>\n", "--------------------------------------------------------------------------------\n", "\n", "[1] Role: MessageRole.USER\n", "Content: 说说没钱的时候应该怎么办？\n", "--------------------------------------------------------------------------------\n", "\n", "[2] Role: MessageRole.ASSISTANT\n", "Content: 在经济紧张时，可以考虑以下几种策略：\n", "\n", "1. **制定预算**：\n", "   - 记录收入和支出，找出可削减的开支。\n", "   - 优先考虑必要的支出，如食物和住房。\n", "\n", "2. **寻找额外收入**：\n", "   - 考虑兼职工作或自由职业，利用自己的技能。\n", "   - 出售不再需要的物品，获取一些现金。\n", "\n", "3. **利用资源**：\n", "   - 寻找社区资源，如食品银行或社会服务机构。\n", "   - 询问朋友和家人是否可以提供短期帮助。\n", "\n", "4. **学习理财知识**：\n", "   - 阅读相关书籍或参加在线课程，提升自己的财务管理能力。\n", "   - 了解如何投资和储蓄，以便未来更好地应对经济压力。\n", "\n", "5. **保持积极心态**：\n", "   - 设定小目标，逐步改善经济状况。\n", "   - 与他人分享你的经历，获得支持和建议。\n", "\n", "这些方法可以帮助你在经济紧张时更好地应对挑战。\n", "--------------------------------------------------------------------------------\n", "\n", "🔍 Facts 原始内容:\n", "\n", "learner_facts Facts (3 个):\n", "  [1] 用户对HTML、CSS、JavaScript了解比较多\n", "  [2] 用户的目标是拯救全世界\n", "  [3] 用户在经济紧张时寻求建议\n", "========================================================================================================================\n"]}], "source": ["# 用panel显示llamaindex的输出内容\n", "\n", "# === 使用 Panel 美化输出显示 ================================================\n", "import panel as pn\n", "pn.extension()\n", "from rich.console import Console\n", "from rich.panel import Panel\n", "from rich.text import Text\n", "from rich.table import Table\n", "from rich.columns import Columns\n", "\n", "def print_memory_details_rich(memory, round_name=\"\"):\n", "    \"\"\"使用 Rich 美化输出 memory 的详细信息\"\"\"\n", "    console = Console(width=120)  # 增加控制台宽度\n", "    \n", "    # 创建主标题\n", "    title = Text(f\"🧠 Memory 观测 - {round_name}\", style=\"bold magenta\")\n", "    \n", "    # Memory.get() 消息表格 - 增加宽度，不截断内容\n", "    msg_table = Table(title=\"Memory.get() - 最终传给LLM的消息\", show_header=True, header_style=\"bold blue\", width=120)\n", "    msg_table.add_column(\"序号\", style=\"dim\", width=6)\n", "    msg_table.add_column(\"角色\", style=\"cyan\", width=20)\n", "    msg_table.add_column(\"内容\", style=\"white\", no_wrap=False)  # 允许换行\n", "    \n", "    for i, m in enumerate(memory.get()):\n", "        role = getattr(m, \"role\", \"\")\n", "        content = getattr(m, \"content\", \"\")\n", "        msg_table.add_row(str(i), str(role), content)\n", "    \n", "    # Memory Blocks 详情表格 - 完整显示所有内容\n", "    blocks_table = Table(title=\"Memory Blocks 详情\", show_header=True, header_style=\"bold green\", width=120)\n", "    blocks_table.add_column(\"块名称\", style=\"yellow\", width=20)\n", "    blocks_table.add_column(\"类型\", style=\"cyan\", width=25)\n", "    blocks_table.add_column(\"内容\", style=\"white\", no_wrap=False)  # 允许换行，完整显示\n", "    \n", "    for blk in memory.memory_blocks:\n", "        block_content = \"\"\n", "        if hasattr(blk, \"static_content\"):\n", "            block_content += f\"静态内容:\\n{blk.static_content}\\n\\n\"\n", "        if hasattr(blk, \"facts\"):\n", "            facts = getattr(blk, 'facts', [])\n", "            block_content += f\"事实数量: {len(facts)}\\n\"\n", "            for j, fact in enumerate(facts, 1):\n", "                block_content += f\"[{j}] {fact}\\n\"  # 完整显示每个fact\n", "        \n", "        blocks_table.add_row(blk.name, type(blk).__name__, block_content.strip())\n", "    \n", "    # Memory 统计信息表格\n", "    stats_table = Table(title=\"Memory 统计信息\", show_header=True, header_style=\"bold red\", width=120)\n", "    stats_table.add_column(\"配置项\", style=\"yellow\", width=30)\n", "    stats_table.add_column(\"值\", style=\"white\", width=80)\n", "    \n", "    stats_table.add_row(\"session_id\", str(memory.session_id))\n", "    stats_table.add_row(\"token_limit\", str(memory.token_limit))\n", "    stats_table.add_row(\"token_flush_size\", str(memory.token_flush_size))\n", "    stats_table.add_row(\"chat_history_token_ratio\", str(memory.chat_history_token_ratio))\n", "    \n", "    # 使用 Panel 包装并显示\n", "    console.print(Panel(title, expand=False))\n", "    console.print(msg_table)\n", "    console.print()\n", "    console.print(blocks_table)\n", "    console.print()\n", "    console.print(stats_table)\n", "    console.print(\"=\" * 120)\n", "\n", "# === 多轮问答（RAG + 共享记忆）with <PERSON> 美化输出 ===========================\n", "from llama_index.core.llms import ChatMessage, MessageRole\n", "\n", "console = Console(width=120)  # 增加控制台宽度\n", "\n", "console.print(Panel(\"🗣️ [Round 1 - RAG] 如何理解python中所说的参数\", style=\"bold blue\", width=120))\n", "resp1 = condense_engine.chat(\"如何理解python中所说的参数，感觉是个很难理解的概念呢！\")\n", "console.print(Panel(f\"🔎 condense_question 答：\\n{resp1}\", style=\"green\", width=120))\n", "\n", "print_memory_details_rich(memory, \"Round 1\")\n", "\n", "console.print(Panel(\"🗣️ [Round 2 - 记录事实] 个人背景信息\", style=\"bold blue\", width=120))\n", "resp2 = condense_engine.chat(\"我对html、css、js了解比较多，还有，我的目标是拯救全世界，你要记住。\")\n", "console.print(Panel(f\"🧠 答：\\n{resp2}\", style=\"green\", width=120))\n", "print_memory_details_rich(memory, \"Round 2\")\n", "\n", "console.print(Panel(\"🗣️ [Round 2.5 - 追加事实] 教学风格偏好\", style=\"bold blue\", width=120))\n", "resp2_5 = condense_engine.chat(\"你在跟我讲解的时候要用理查德·费曼的风格，你要记住。\")\n", "console.print(Panel(f\"🧠 答：\\n{resp2_5}\", style=\"green\", width=120))\n", "print_memory_details_rich(memory, \"Round 2.5\")\n", "\n", "console.print(Panel(\"🗣️ [Round 3 - 无检索，仅用记忆] 生活问题\", style=\"bold blue\", width=120))\n", "resp3 = simple_engine.chat(\"说说没钱的时候应该怎么办？\")\n", "console.print(Panel(f\"💬 simple 答：\\n{resp3}\", style=\"green\", width=120))\n", "\n", "print_memory_details_rich(memory, \"Round 3\")\n", "\n", "# === 额外：直接打印完整内容（无格式化） ===========================\n", "print(\"\\n\" + \"=\"*120)\n", "print(\"📋 原始 Memory 内容（无格式化，完整显示）\")\n", "print(\"=\"*120)\n", "\n", "print(\"\\n🔍 Memory.get() 原始内容:\")\n", "for i, m in enumerate(memory.get()):\n", "    print(f\"\\n[{i}] Role: {getattr(m, 'role', '')}\")\n", "    print(f\"Content: {getattr(m, 'content', '')}\")\n", "    print(\"-\" * 80)\n", "\n", "print(\"\\n🔍 Facts 原始内容:\")\n", "for blk in memory.memory_blocks:\n", "    if hasattr(blk, \"facts\"):\n", "        facts = getattr(blk, 'facts', [])\n", "        print(f\"\\n{blk.name} Facts ({len(facts)} 个):\")\n", "        for j, fact in enumerate(facts, 1):\n", "            print(f\"  [{j}] {fact}\")\n", "\n", "print(\"=\"*120)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}