# 注意，在jupyternotebook不能显示message和response调试信息

import os
from llama_index.core import Settings
import llama_index.core
from llama_index.llms.openai import OpenAI
from llama_index.embeddings.openai import OpenAIEmbedding

llama_index.core.set_global_handler("simple")  # 打印所有 LLM 的输入/输出

# 替换成你自己的 OpenAI API Key
os.environ["OPENAI_API_KEY"] = "sk-qY0nr8zudg7Wc2bTR8EUV6rOTwfqZlU2ihwGL4pJ6m2ZEkEE"
# 如需自定义网关（如代理或 Azure），取消下面注释并替换
os.environ["OPENAI_BASE_URL"] = "https://api.openai-proxy.org/v1"



# ➊ 配置全局设置（替代 ServiceContext）
Settings.llm = OpenAI(
    model="gpt-4o-mini", 
    temperature=0.1,
    api_base="https://api.openai-proxy.org/v1"
)
Settings.embed_model = OpenAIEmbedding(
    model="text-embedding-3-small",
    api_base="https://api.openai-proxy.org/v1"
)

from llama_index.core import Document, VectorStoreIndex
from llama_index.vector_stores.qdrant import QdrantVectorStore
from qdrant_client import QdrantClient


# 准备知识库文档
documents = [
    Document(
        text="FlexiBot的第一个核心功能是多语言翻译。它可以实时翻译超过50种语言，帮助用户跨越语言障碍进行交流。",
        metadata={"doc_name": "功能介绍", "feature_id": "translation"}
    ),
    Document(
        text="FlexiBot的第二个能力是代码生成。它精通Python、JavaScript和SQL，可以根据用户的自然语言描述生成相应的代码片段。",
        metadata={"doc_name": "功能介绍", "feature_id": "coding"}
    ),
    Document(
        text="FlexiBot的第三个亮点是创意写作。无论是市场营销文案、诗歌还是短篇故事，它都能提供富有创意的文本内容。",
        metadata={"doc_name": "功能介绍", "feature_id": "writing"}
    )
]

# 初始化一个内存中的 Qdrant 客户端
client = QdrantClient(location=":memory:")

# 创建一个 QdrantVectorStore 实例
vector_store = QdrantVectorStore(client=client, collection_name="flexibot_docs")

# 从文档创建索引，数据将自动存入 Qdrant
index = VectorStoreIndex.from_documents(
    documents,
    vector_store=vector_store,
)

print("知识库已成功索引到 Qdrant (内存模式)！")

from llama_index.core.memory import ChatMemoryBuffer

# 创建一个内存来存储对话历史
memory = ChatMemoryBuffer.from_defaults(token_limit=2000)

# 从索引创建上下文聊天引擎
context_chat_engine = index.as_chat_engine(
    # chat_mode="context",
    chat_mode="condense_question", #总体看这个模式更加合理
    # chat_mode="simple", #这个是随便一问一答模式,不用知识库
    # chat_mode="condense_plus_context", #没看懂这个是干嘛的，感觉浪费资源
    memory=memory,
    # system_prompt="你是一个名叫 FlexiBot 的乐于助人的AI助手。请根据上下文回答问题。",
    verbose=True
)

# 开始第一轮对话
print("--- 开始对话 (ContextChatEngine) ---\n")
user_message_1 = "FlexiBot有什么功能？"

print('-'*20,"用户message start",'-'*20)
print(f"👤 用户: {user_message_1}")
print('-'*20,"用户message end",'-'*20, "\n\n")

response_1 = context_chat_engine.chat(user_message_1)

print('-'*20,"AI response start",'-'*20)
print(f"🤖 FlexiBot: {response_1}\n")
print('-'*20,"AI response end",'-'*20, "\n\n")

# 第二轮对话，提出一个依赖上一轮对话的内容
user_message_2 = "详细介绍一下第二个功能。"

print('-'*20,"用户message start",'-'*20)
print(f"👤 用户: {user_message_2}")
print('-'*20,"用户message end",'-'*20, "\n\n")

response_2 = context_chat_engine.chat(user_message_2)

print('-'*20,"AI response start",'-'*20)
print(f"🤖 FlexiBot: {response_2}\n")
print('-'*20,"AI response end",'-'*20, "\n\n")

print("--- 对话结束 ---")