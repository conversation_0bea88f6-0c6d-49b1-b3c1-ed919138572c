{"cells": [{"cell_type": "markdown", "id": "intro-cell", "metadata": {}, "source": ["# 💡 用 LlamaIndex 与 Qdrant 处理对话历史\n", "\n", "在构建一个问答机器人或AI助手时，仅仅能够回答单个问题是不够的。一个优秀的AI需要具备**上下文记忆能力**，能够理解并记住之前的对话内容，从而进行连贯的多轮交流。\n", "\n", "本 `ipynb` 将演示如何使用 **LlamaIndex** 和 **Qdrant** 向量数据库来处理和利用对话历史信息。我们将探索 LlamaIndex 中处理对话的两种核心模式：\n", "\n", "1.  **上下文聊天引擎 (`ContextChatEngine`)**: 在每次对话时，将历史消息和检索到的知识库内容一起作为上下文提供给大语言模型。\n", "2.  **问题压缩聊天引擎 (`CondenseQuestionChatEngine`)**: 在每次对话时，首先根据历史消息将用户的后续问题“重写”成一个独立的、无须上下文就能理解的新问题，然后再用这个新问题去检索知识库。\n", "\n", "我们将使用一个本地内存中的 Qdrant 实例来存储向量，让你无需额外配置即可运行整个流程。\n", "\n", "---"]}, {"attachments": {"image.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "dffd4b75", "metadata": {}, "source": ["# 对比三种形式\n", "\n", "## 我更倾向于第二种，省token而且幻觉少\n", "\n", "![image.png](attachment:image.png)"]}, {"cell_type": "markdown", "id": "install-deps", "metadata": {}, "source": ["## 0️⃣ 安装依赖\n", "\n", "首先，确保我们安装了所有必需的 Python 库。如果已经安装，可以跳过此步骤。"]}, {"cell_type": "code", "execution_count": 1, "id": "pip-install", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: llama-index in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (0.13.0)\n", "Requirement already satisfied: qdrant-client in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (1.15.1)\n", "Requirement already satisfied: llama-index-vector-stores-qdrant in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (0.7.1)\n", "Requirement already satisfied: llama-index-llms-openai in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (0.5.0)\n", "Collecting llama-index-llms-openai\n", "  Downloading llama_index_llms_openai-0.5.1-py3-none-any.whl.metadata (3.0 kB)\n", "Requirement already satisfied: llama-index-embeddings-openai in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (0.5.0)\n", "Requirement already satisfied: llama-index-cli<0.6,>=0.5.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index) (0.5.0)\n", "Requirement already satisfied: llama-index-core<0.14,>=0.13.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index) (0.13.0)\n", "Requirement already satisfied: llama-index-indices-managed-llama-cloud>=0.4.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index) (0.9.0)\n", "Requirement already satisfied: llama-index-readers-file<0.6,>=0.5.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index) (0.5.0)\n", "Requirement already satisfied: llama-index-readers-llama-parse>=0.4.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index) (0.5.0)\n", "Requirement already satisfied: nltk>3.8.1 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index) (3.9.1)\n", "Requirement already satisfied: openai<2,>=1.81.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-llms-openai) (1.99.1)\n", "Requirement already satisfied: aiohttp<4,>=3.8.6 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-core<0.14,>=0.13.0->llama-index) (3.12.14)\n", "Requirement already satisfied: aiosqlite in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-core<0.14,>=0.13.0->llama-index) (0.21.0)\n", "Requirement already satisfied: banks<3,>=2.2.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-core<0.14,>=0.13.0->llama-index) (2.2.0)\n", "Requirement already satisfied: dataclasses-json in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-core<0.14,>=0.13.0->llama-index) (0.6.7)\n", "Requirement already satisfied: deprecated>=1.2.9.3 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-core<0.14,>=0.13.0->llama-index) (1.2.18)\n", "Requirement already satisfied: <PERSON><PERSON><PERSON><2,>=1.0.8 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-core<0.14,>=0.13.0->llama-index) (1.0.8)\n", "Requirement already satisfied: filetype<2,>=1.2.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-core<0.14,>=0.13.0->llama-index) (1.2.0)\n", "Requirement already satisfied: fsspec>=2023.5.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-core<0.14,>=0.13.0->llama-index) (2025.7.0)\n", "Requirement already satisfied: httpx in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-core<0.14,>=0.13.0->llama-index) (0.28.1)\n", "Requirement already satisfied: llama-index-workflows<2,>=1.0.1 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-core<0.14,>=0.13.0->llama-index) (1.2.0)\n", "Requirement already satisfied: nest-asyncio<2,>=1.5.8 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-core<0.14,>=0.13.0->llama-index) (1.6.0)\n", "Requirement already satisfied: networkx>=3.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-core<0.14,>=0.13.0->llama-index) (3.5)\n", "Requirement already satisfied: numpy in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-core<0.14,>=0.13.0->llama-index) (2.3.2)\n", "Requirement already satisfied: pillow>=9.0.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-core<0.14,>=0.13.0->llama-index) (11.3.0)\n", "Requirement already satisfied: platformdirs in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-core<0.14,>=0.13.0->llama-index) (4.3.8)\n", "Requirement already satisfied: pydantic>=2.8.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-core<0.14,>=0.13.0->llama-index) (2.11.7)\n", "Requirement already satisfied: pyyaml>=6.0.1 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-core<0.14,>=0.13.0->llama-index) (6.0.2)\n", "Requirement already satisfied: requests>=2.31.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-core<0.14,>=0.13.0->llama-index) (2.32.4)\n", "Requirement already satisfied: setuptools>=80.9.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-core<0.14,>=0.13.0->llama-index) (80.9.0)\n", "Requirement already satisfied: sqlalchemy>=1.4.49 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from sqlalchemy[asyncio]>=1.4.49->llama-index-core<0.14,>=0.13.0->llama-index) (2.0.42)\n", "Requirement already satisfied: tenacity!=8.4.0,<10.0.0,>=8.2.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-core<0.14,>=0.13.0->llama-index) (8.5.0)\n", "Requirement already satisfied: tiktoken>=0.7.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-core<0.14,>=0.13.0->llama-index) (0.9.0)\n", "Requirement already satisfied: tqdm<5,>=4.66.1 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-core<0.14,>=0.13.0->llama-index) (4.67.1)\n", "Requirement already satisfied: typing-extensions>=4.5.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-core<0.14,>=0.13.0->llama-index) (4.14.1)\n", "Requirement already satisfied: typing-inspect>=0.8.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-core<0.14,>=0.13.0->llama-index) (0.9.0)\n", "Requirement already satisfied: wrapt in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-core<0.14,>=0.13.0->llama-index) (1.17.2)\n", "Requirement already satisfied: aiohappyeyeballs>=2.5.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from aiohttp<4,>=3.8.6->llama-index-core<0.14,>=0.13.0->llama-index) (2.6.1)\n", "Requirement already satisfied: aiosignal>=1.4.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from aiohttp<4,>=3.8.6->llama-index-core<0.14,>=0.13.0->llama-index) (1.4.0)\n", "Requirement already satisfied: attrs>=17.3.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from aiohttp<4,>=3.8.6->llama-index-core<0.14,>=0.13.0->llama-index) (25.3.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from aiohttp<4,>=3.8.6->llama-index-core<0.14,>=0.13.0->llama-index) (1.7.0)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from aiohttp<4,>=3.8.6->llama-index-core<0.14,>=0.13.0->llama-index) (6.6.3)\n", "Requirement already satisfied: propcache>=0.2.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from aiohttp<4,>=3.8.6->llama-index-core<0.14,>=0.13.0->llama-index) (0.3.2)\n", "Requirement already satisfied: yarl<2.0,>=1.17.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from aiohttp<4,>=3.8.6->llama-index-core<0.14,>=0.13.0->llama-index) (1.20.1)\n", "Requirement already satisfied: griffe in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from banks<3,>=2.2.0->llama-index-core<0.14,>=0.13.0->llama-index) (1.9.0)\n", "Requirement already satisfied: jinja2 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from banks<3,>=2.2.0->llama-index-core<0.14,>=0.13.0->llama-index) (3.1.6)\n", "Requirement already satisfied: beautifulsoup4<5,>=4.12.3 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-readers-file<0.6,>=0.5.0->llama-index) (4.13.4)\n", "Requirement already satisfied: defusedxml>=0.7.1 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-readers-file<0.6,>=0.5.0->llama-index) (0.7.1)\n", "Requirement already satisfied: pandas<2.3.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-readers-file<0.6,>=0.5.0->llama-index) (2.2.3)\n", "Requirement already satisfied: pypdf<6,>=5.1.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-readers-file<0.6,>=0.5.0->llama-index) (5.9.0)\n", "Requirement already satisfied: striprtf<0.0.27,>=0.0.26 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-readers-file<0.6,>=0.5.0->llama-index) (0.0.26)\n", "Requirement already satisfied: soupsieve>1.2 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from beautifulsoup4<5,>=4.12.3->llama-index-readers-file<0.6,>=0.5.0->llama-index) (2.7)\n", "Requirement already satisfied: llama-index-instrumentation>=0.1.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-workflows<2,>=1.0.1->llama-index-core<0.14,>=0.13.0->llama-index) (0.4.0)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from openai<2,>=1.81.0->llama-index-llms-openai) (4.9.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from openai<2,>=1.81.0->llama-index-llms-openai) (1.9.0)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from openai<2,>=1.81.0->llama-index-llms-openai) (0.10.0)\n", "Requirement already satisfied: sniffio in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from openai<2,>=1.81.0->llama-index-llms-openai) (1.3.1)\n", "Requirement already satisfied: idna>=2.8 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from anyio<5,>=3.5.0->openai<2,>=1.81.0->llama-index-llms-openai) (3.10)\n", "Requirement already satisfied: certifi in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from httpx->llama-index-core<0.14,>=0.13.0->llama-index) (2025.7.14)\n", "Requirement already satisfied: httpcore==1.* in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from httpx->llama-index-core<0.14,>=0.13.0->llama-index) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from httpcore==1.*->httpx->llama-index-core<0.14,>=0.13.0->llama-index) (0.16.0)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from pandas<2.3.0->llama-index-readers-file<0.6,>=0.5.0->llama-index) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from pandas<2.3.0->llama-index-readers-file<0.6,>=0.5.0->llama-index) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from pandas<2.3.0->llama-index-readers-file<0.6,>=0.5.0->llama-index) (2025.2)\n", "Requirement already satisfied: annotated-types>=0.6.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from pydantic>=2.8.0->llama-index-core<0.14,>=0.13.0->llama-index) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from pydantic>=2.8.0->llama-index-core<0.14,>=0.13.0->llama-index) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from pydantic>=2.8.0->llama-index-core<0.14,>=0.13.0->llama-index) (0.4.1)\n", "Requirement already satisfied: colorama in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from tqdm<5,>=4.66.1->llama-index-core<0.14,>=0.13.0->llama-index) (0.4.6)\n", "Requirement already satisfied: grpcio>=1.41.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from qdrant-client) (1.74.0)\n", "Requirement already satisfied: portalocker<4.0,>=2.7.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from qdrant-client) (2.10.1)\n", "Requirement already satisfied: protobuf>=3.20.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from qdrant-client) (6.31.1)\n", "Requirement already satisfied: urllib3<3,>=1.26.14 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from qdrant-client) (2.5.0)\n", "Requirement already satisfied: pywin32>=226 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from portalocker<4.0,>=2.7.0->qdrant-client) (311)\n", "Requirement already satisfied: h2<5,>=3 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from httpx[http2]>=0.20.0->qdrant-client) (4.2.0)\n", "Requirement already satisfied: hyperframe<7,>=6.1 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from h2<5,>=3->httpx[http2]>=0.20.0->qdrant-client) (6.1.0)\n", "Requirement already satisfied: hpack<5,>=4.1 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from h2<5,>=3->httpx[http2]>=0.20.0->qdrant-client) (4.1.0)\n", "Requirement already satisfied: llama-cloud==0.1.35 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-indices-managed-llama-cloud>=0.4.0->llama-index) (0.1.35)\n", "Requirement already satisfied: llama-parse>=0.5.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-index-readers-llama-parse>=0.4.0->llama-index) (0.6.54)\n", "Requirement already satisfied: llama-cloud-services>=0.6.54 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-parse>=0.5.0->llama-index-readers-llama-parse>=0.4.0->llama-index) (0.6.54)\n", "Requirement already satisfied: click<9,>=8.1.7 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-cloud-services>=0.6.54->llama-parse>=0.5.0->llama-index-readers-llama-parse>=0.4.0->llama-index) (8.2.1)\n", "Requirement already satisfied: python-dotenv<2,>=1.0.1 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from llama-cloud-services>=0.6.54->llama-parse>=0.5.0->llama-index-readers-llama-parse>=0.4.0->llama-index) (1.1.1)\n", "Requirement already satisfied: joblib in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from nltk>3.8.1->llama-index) (1.5.1)\n", "Requirement already satisfied: regex>=2021.8.3 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from nltk>3.8.1->llama-index) (2024.11.6)\n", "Requirement already satisfied: six>=1.5 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from python-dateutil>=2.8.2->pandas<2.3.0->llama-index-readers-file<0.6,>=0.5.0->llama-index) (1.17.0)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from requests>=2.31.0->llama-index-core<0.14,>=0.13.0->llama-index) (3.4.2)\n", "Requirement already satisfied: greenlet>=1 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from sqlalchemy>=1.4.49->sqlalchemy[asyncio]>=1.4.49->llama-index-core<0.14,>=0.13.0->llama-index) (3.2.3)\n", "Requirement already satisfied: mypy-extensions>=0.3.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from typing-inspect>=0.8.0->llama-index-core<0.14,>=0.13.0->llama-index) (1.1.0)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from dataclasses-json->llama-index-core<0.14,>=0.13.0->llama-index) (3.26.1)\n", "Requirement already satisfied: packaging>=17.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from marshmallow<4.0.0,>=3.18.0->dataclasses-json->llama-index-core<0.14,>=0.13.0->llama-index) (25.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in c:\\users\\<USER>\\desktop\\ai_mindmap\\.venv\\lib\\site-packages (from jinja2->banks<3,>=2.2.0->llama-index-core<0.14,>=0.13.0->llama-index) (3.0.2)\n", "Downloading llama_index_llms_openai-0.5.1-py3-none-any.whl (25 kB)\n", "Installing collected packages: llama-index-llms-openai\n", "  Attempting uninstall: llama-index-llms-openai\n", "    Found existing installation: llama-index-llms-openai 0.5.0\n", "    Uninstalling llama-index-llms-openai-0.5.0:\n", "      Successfully uninstalled llama-index-llms-openai-0.5.0\n", "Successfully installed llama-index-llms-openai-0.5.1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "[notice] A new release of pip is available: 25.1.1 -> 25.2\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}], "source": ["!pip install -U llama-index qdrant-client llama-index-vector-stores-qdrant llama-index-llms-openai llama-index-embeddings-openai"]}, {"cell_type": "markdown", "id": "setup-keys", "metadata": {}, "source": ["## 1️⃣ 配置环境\n", "\n", "设置你的 OpenAI API 密钥。为了安全，建议使用环境变量，但为方便演示，我们在此直接设置。\n", "\n", "⚠️ **安全警告**: 请勿将你的 API 密钥直接硬编码到生产代码或公开的代码仓库中。"]}, {"cell_type": "code", "execution_count": 16, "id": "set-env", "metadata": {}, "outputs": [], "source": ["import os\n", "import llama_index.core\n", "llama_index.core.set_global_handler(\"simple\")  # 打印所有 LLM 的输入/输出\n", "from llama_index.core import Settings\n", "from llama_index.llms.openai import OpenAI\n", "from llama_index.embeddings.openai import OpenAIEmbedding\n", "\n", "# 替换成你自己的 OpenAI API Key\n", "os.environ[\"OPENAI_API_KEY\"] = \"sk-qY0nr8zudg7Wc2bTR8EUV6rOTwfqZlU2ihwGL4pJ6m2ZEkEE\"\n", "# 如需自定义网关（如代理或 Azure），取消下面注释并替换\n", "os.environ[\"OPENAI_BASE_URL\"] = \"https://api.openai-proxy.org/v1\"\n", "\n", "\n", "\n", "# ➊ 配置全局设置（替代 ServiceContext）\n", "Settings.llm = OpenAI(\n", "    model=\"gpt-4o-mini\", \n", "    temperature=0,\n", "    api_base=\"https://api.openai-proxy.org/v1\"\n", ")\n", "Settings.embed_model = OpenAIEmbedding(\n", "    model=\"text-embedding-3-small\",\n", "    api_base=\"https://api.openai-proxy.org/v1\"\n", ")\n"]}, {"cell_type": "markdown", "id": "prepare-data", "metadata": {}, "source": ["## 2️⃣ 准备数据与索引\n", "\n", "我们将创建一个简单的知识库，内容是关于一个名叫“FlexiBot”的AI助手的功能介绍。然后，我们将这些内容向量化并存储到 Qdrant 向量数据库中。"]}, {"cell_type": "code", "execution_count": 17, "id": "create-index", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["知识库已成功索引到 Qdrant (内存模式)！\n"]}], "source": ["from llama_index.core import Document, VectorStoreIndex\n", "from llama_index.vector_stores.qdrant import QdrantVectorStore\n", "from qdrant_client import QdrantClient\n", "\n", "\n", "# 准备知识库文档\n", "documents = [\n", "    Document(\n", "        text=\"FlexiBot的第一个核心功能是多语言翻译。它可以实时翻译超过50种语言，帮助用户跨越语言障碍进行交流。\",\n", "        metadata={\"doc_name\": \"功能介绍\", \"feature_id\": \"translation\"}\n", "    ),\n", "    Document(\n", "        text=\"FlexiBot的第二个能力是代码生成。它精通Python、JavaScript和SQL，可以根据用户的自然语言描述生成相应的代码片段。\",\n", "        metadata={\"doc_name\": \"功能介绍\", \"feature_id\": \"coding\"}\n", "    ),\n", "    Document(\n", "        text=\"FlexiBot的第三个亮点是创意写作。无论是市场营销文案、诗歌还是短篇故事，它都能提供富有创意的文本内容。\",\n", "        metadata={\"doc_name\": \"功能介绍\", \"feature_id\": \"writing\"}\n", "    )\n", "]\n", "\n", "# 初始化一个内存中的 Qdrant 客户端\n", "client = QdrantClient(location=\":memory:\")\n", "\n", "# 创建一个 QdrantVectorStore 实例\n", "vector_store = QdrantVectorStore(client=client, collection_name=\"flexibot_docs\")\n", "\n", "# 从文档创建索引，数据将自动存入 Qdrant\n", "index = VectorStoreIndex.from_documents(\n", "    documents,\n", "    vector_store=vector_store,\n", ")\n", "\n", "print(\"知识库已成功索引到 Qdrant (内存模式)！\")"]}, {"cell_type": "markdown", "id": "markdown-method-1", "metadata": {}, "source": ["## 3️⃣ 方法一: `ContextChatEngine`\n", "\n", "这是最直接的对话模式。它的工作流程如下：\n", "\n", "1.  接收到用户的消息。\n", "2.  使用该消息去知识库（Qdrant）中检索最相关的信息。\n", "3.  将 **原始对话历史** + **检索到的知识** + **最新用户消息** 一同打包，发送给大语言模型生成回复。\n", "\n", "这种模式的优点是实现简单，但缺点是检索过程没有利用到对话历史。如果用户的问题高度依赖上下文（例如“它怎么样？”），检索效果可能不佳。\n", "\n", "### ContextChatEngine的工作机制\n", "检索阶段：只用当前用户消息去检索，不考虑对话历史\n", "生成阶段：把对话历史 + 检索结果 + 当前消息一起发给LLM\n", "\n", "所以矛盾在于：虽然LLM能看到完整对话历史，但检索时没用到历史信息，可能检索不到最相关的内容。"]}, {"cell_type": "code", "execution_count": 18, "id": "code-method-1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- 开始对话 (ContextChatEngine) ---\n", "\n", "-------------------- 用户message start --------------------\n", "👤 用户: FlexiBot有什么功能？\n", "-------------------- 用户message end -------------------- \n", "\n", "\n", "-------------------- AI response start --------------------\n", "🤖 FlexiBot: FlexiBot有两个主要功能：\n", "\n", "1. **多语言翻译**：它可以实时翻译超过50种语言，帮助用户跨越语言障碍进行交流。\n", "\n", "2. **代码生成**：FlexiBot精通Python、JavaScript和SQL，可以根据用户的自然语言描述生成相应的代码片段。\n", "\n", "-------------------- AI response end -------------------- \n", "\n", "\n", "-------------------- 用户message start --------------------\n", "👤 用户: 详细介绍一下第二个功能。\n", "-------------------- 用户message end -------------------- \n", "\n", "\n", "-------------------- AI response start --------------------\n", "🤖 FlexiBot: FlexiBot的第二个功能是**代码生成**。这个功能使得FlexiBot能够根据用户的自然语言描述生成相应的代码片段。它精通以下编程语言：\n", "\n", "- **Python**：适用于数据分析、机器学习、Web开发等多种应用场景。\n", "- **JavaScript**：广泛用于前端开发和后端开发，能够创建动态网页和应用程序。\n", "- **SQL**：用于数据库查询和管理，能够帮助用户进行数据操作和分析。\n", "\n", "用户只需用自然语言描述他们想要实现的功能或逻辑，FlexiBot就能理解并生成相应的代码，极大地提高了编程的效率和便利性。这对于开发者、学生或任何需要编写代码的人来说，都是一个非常实用的工具。\n", "\n", "-------------------- AI response end -------------------- \n", "\n", "\n", "--- 对话结束 ---\n"]}], "source": ["from llama_index.core.memory import ChatMemoryBuffer\n", "\n", "# 创建一个内存来存储对话历史\n", "memory = ChatMemoryBuffer.from_defaults(token_limit=2000)\n", "\n", "# 从索引创建上下文聊天引擎\n", "context_chat_engine = index.as_chat_engine(\n", "    chat_mode=\"context\",\n", "    memory=memory,\n", "    system_prompt=\"你是一个名叫 FlexiBot 的乐于助人的AI助手。请根据上下文回答问题。\",\n", "    verbose=True\n", ")\n", "\n", "# 开始第一轮对话\n", "print(\"--- 开始对话 (ContextChatEngine) ---\\n\")\n", "user_message_1 = \"FlexiBot有什么功能？\"\n", "\n", "print('-'*20,\"用户message start\",'-'*20)\n", "print(f\"👤 用户: {user_message_1}\")\n", "print('-'*20,\"用户message end\",'-'*20, \"\\n\\n\")\n", "\n", "response_1 = context_chat_engine.chat(user_message_1)\n", "\n", "print('-'*20,\"AI response start\",'-'*20)\n", "print(f\"🤖 FlexiBot: {response_1}\\n\")\n", "print('-'*20,\"AI response end\",'-'*20, \"\\n\\n\")\n", "\n", "# 第二轮对话，提出一个依赖上一轮对话的内容\n", "user_message_2 = \"详细介绍一下第二个功能。\"\n", "\n", "print('-'*20,\"用户message start\",'-'*20)\n", "print(f\"👤 用户: {user_message_2}\")\n", "print('-'*20,\"用户message end\",'-'*20, \"\\n\\n\")\n", "\n", "response_2 = context_chat_engine.chat(user_message_2)\n", "\n", "print('-'*20,\"AI response start\",'-'*20)\n", "print(f\"🤖 FlexiBot: {response_2}\\n\")\n", "print('-'*20,\"AI response end\",'-'*20, \"\\n\\n\")\n", "\n", "print(\"--- 对话结束 ---\")"]}, {"cell_type": "markdown", "id": "52299954", "metadata": {}, "source": ["# ContextChatEngine 实现说明\n", "\n", "## 代码性质说明\n", "\n", "这里的 `VerboseContextChatEngine` **不是**完全参考 LlamaIndex 源码的实现，而是**模拟**了其核心逻辑的教学版本。\n", "\n", "## 真实 vs 模拟版本对比\n", "\n", "### 真实的 ContextChatEngine 内部流程\n", "1. **检索**：用当前消息检索相关文档\n", "2. **构建上下文**：将检索结果格式化\n", "3. **合并历史**：从 `ChatMemoryBuffer` 获取对话历史\n", "4. **生成 Prompt**：使用内置的 prompt 模板\n", "5. **调用 LLM**：发送给配置的 LLM\n", "6. **更新记忆**：保存新的对话轮次\n", "\n", "### 我的模拟版本对比\n", "\n", "| 功能模块 | 实现程度 | 说明 |\n", "|---------|---------|------|\n", "| ✅ 检索逻辑 | 完全一样 | 用 `retriever.retrieve(message)` |\n", "| ✅ 记忆管理 | 完全一样 | 用 `ChatMemoryBuffer` |\n", "| ✅ 核心流程 | 基本一致 | 检索→构建→生成→更新 |\n", "| ❌ Prompt 模板 | 简化版本 | 真实版本更复杂 |\n", "| ❌ 错误处理 | 省略大部分 | 缺少边界情况处理 |\n", "| ✅ 关键洞察 | 完全展示 | \"检索不用历史，但生成时用历史\"的矛盾 |\n", "\n", "## 代码目的\n", "\n", "这是**教学版本**，核心逻辑正确但比真实实现简单很多。主要目的是：\n", "\n", "- 🎯 **透明化**：清楚展示 ContextChatEngine 到底把什么内容发给了 LLM\n", "- 🔍 **可观察**：通过打印语句看到每个步骤的具体内容\n", "- 📚 **理解核心**：掌握 RAG 多轮对话的本质机制\n", "\n", "> **注意**：生产环境请使用 LlamaIndex 官方的 `ContextChatEngine`，它有更完善的错误处理和优化。"]}, {"cell_type": "code", "execution_count": 9, "id": "8c589def", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 第一轮对话 ===\n", "🔍 检索查询: '什么水果是红色的？'\n", "📚 检索到的知识:\n", "苹果是红色的水果，富含维生素C\n", "橙子是橙色的水果，酸甜可口\n", "\n", "📝 发送给LLM的完整内容:\n", "--------------------------------------------------\n", "基于以下知识回答问题：\n", "\n", "知识库内容：\n", "苹果是红色的水果，富含维生素C\n", "橙子是橙色的水果，酸甜可口\n", "\n", "对话历史：\n", "\n", "\n", "当前问题：什么水果是红色的？\n", "\n", "请回答：\n", "--------------------------------------------------\n", "\n", "🤖 回答: 红色的水果是苹果。\n", "\n", "=== 第二轮对话 ===\n", "🔍 检索查询: '它有什么营养价值？'\n", "📚 检索到的知识:\n", "香蕉是黄色的水果，含有钾元素\n", "苹果是红色的水果，富含维生素C\n", "\n", "📝 发送给LLM的完整内容:\n", "--------------------------------------------------\n", "基于以下知识回答问题：\n", "\n", "知识库内容：\n", "香蕉是黄色的水果，含有钾元素\n", "苹果是红色的水果，富含维生素C\n", "\n", "对话历史：\n", "用户: 什么水果是红色的？\n", "助手: 红色的水果是苹果。\n", "\n", "\n", "当前问题：它有什么营养价值？\n", "\n", "请回答：\n", "--------------------------------------------------\n", "\n", "🤖 回答: 苹果富含维生素C，具有很高的营养价值。维生素C有助于增强免疫系统，促进皮肤健康，并有助于铁的吸收。\n"]}], "source": ["# 创建知识库\n", "documents = [\n", "    Document(text=\"苹果是红色的水果，富含维生素C\"),\n", "    Document(text=\"香蕉是黄色的水果，含有钾元素\"),\n", "    Document(text=\"橙子是橙色的水果，酸甜可口\")\n", "]\n", "\n", "index = VectorStoreIndex.from_documents(documents)\n", "\n", "# 创建自定义聊天引擎，能打印传给LLM的内容\n", "class VerboseContextChatEngine:\n", "    def __init__(self, index):\n", "        self.index = index\n", "        self.memory = ChatMemoryBuffer.from_defaults(token_limit=2000)\n", "        self.retriever = index.as_retriever(similarity_top_k=2)\n", "        \n", "    def chat(self, message):\n", "        # 1. 只用当前消息检索（不用历史）\n", "        print(f\"🔍 检索查询: '{message}'\")\n", "        retrieved_nodes = self.retriever.retrieve(message)\n", "        \n", "        context = \"\\n\".join([node.text for node in retrieved_nodes])\n", "        print(f\"📚 检索到的知识:\\n{context}\\n\")\n", "        \n", "        # 2. 构建完整prompt - 修复这里\n", "        chat_history = \"\"\n", "        for msg in self.memory.get():\n", "            if hasattr(msg, 'content'):\n", "                role = \"用户\" if msg.role == \"user\" else \"助手\"\n", "                chat_history += f\"{role}: {msg.content}\\n\"\n", "            else:\n", "                # 处理字典格式\n", "                role = \"用户\" if msg.get(\"role\") == \"user\" else \"助手\"\n", "                chat_history += f\"{role}: {msg.get('content', '')}\\n\"\n", "            \n", "        full_prompt = f\"\"\"基于以下知识回答问题：\n", "\n", "知识库内容：\n", "{context}\n", "\n", "对话历史：\n", "{chat_history}\n", "\n", "当前问题：{message}\n", "\n", "请回答：\"\"\"\n", "        \n", "        print(f\"📝 发送给LLM的完整内容:\\n{'-'*50}\\n{full_prompt}\\n{'-'*50}\\n\")\n", "        \n", "        # 3. 调用LLM\n", "        response = Settings.llm.complete(full_prompt)\n", "        \n", "        # 4. 更新记忆 - 使用正确的格式\n", "        from llama_index.core.llms import ChatMessage\n", "        self.memory.put(ChatMessage(role=\"user\", content=message))\n", "        self.memory.put(<PERSON><PERSON><PERSON><PERSON><PERSON>(role=\"assistant\", content=str(response)))\n", "        \n", "        return response\n", "\n", "# 测试多轮对话\n", "chat_engine = VerboseContextChatEngine(index)\n", "\n", "print(\"=== 第一轮对话 ===\")\n", "response1 = chat_engine.chat(\"什么水果是红色的？\")\n", "print(f\"🤖 回答: {response1}\\n\")\n", "\n", "print(\"=== 第二轮对话 ===\")\n", "response2 = chat_engine.chat(\"它有什么营养价值？\")\n", "print(f\"🤖 回答: {response2}\")"]}, {"cell_type": "markdown", "id": "markdown-reset", "metadata": {}, "source": ["### 重置对话状态\n", "\n", "聊天引擎是**有状态的**，它会记住当前的对话。在开始一个全新的对话之前，我们需要重置它。"]}, {"cell_type": "code", "execution_count": null, "id": "code-reset", "metadata": {}, "outputs": [], "source": ["print(\"重置聊天引擎状态...\")\n", "context_chat_engine.reset()\n", "print(\"状态已重置，可以开始新的对话了。\")"]}, {"cell_type": "markdown", "id": "markdown-method-2", "metadata": {}, "source": ["## 4️⃣ 方法二: `CondenseQuestionChatEngine`\n", "\n", "这是一种更智能、更鲁棒的对话模式。它的工作流程更复杂：\n", "\n", "1.  接收到用户的最新消息。\n", "2.  **(核心步骤)** LlamaIndex 首先会调用一次大语言模型，让它结合**对话历史**和**最新消息**，生成一个全新的、独立的、信息完整的“浓缩问题”（Condensed Question）。\n", "3.  使用这个“浓缩问题”去知识库（Qdrant）中进行检索。\n", "4.  将 **原始对话历史** + **检索到的知识** + **最新用户消息** 一同发送给大语言模型生成最终回复。\n", "\n", "这种模式通过“问题重写”大大提高了后续检索的准确性，尤其是在处理依赖上下文的模糊问题时效果显著。"]}, {"cell_type": "code", "execution_count": 20, "id": "code-method-2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- 开始对话 (CondenseQuestionChatEngine) ---\n", "\n", "👤 用户: FlexiBot有什么功能？\n", "Querying with: FlexiBot有什么功能？\n", "🤖 FlexiBot: FlexiBot具有多语言翻译和代码生成两个核心功能。它可以实时翻译超过50种语言，帮助用户进行跨语言交流。同时，它也能根据用户的自然语言描述生成Python、JavaScript和SQL代码片段。\n", "\n", "👤 用户: 详细介绍一下第二个功能。\n", "Querying with: 请详细介绍一下FlexiBot的代码生成功能。\n", "🤖 FlexiBot: FlexiBot的代码生成功能能够根据用户的自然语言描述生成相应的代码片段。它精通多种编程语言，包括Python、JavaScript和SQL。这一功能使得用户能够轻松地将他们的想法转化为实际的代码，无需深入了解编程的细节。通过这种方式，FlexiBot为开发者和非开发者提供了便利，帮助他们更高效地完成编程任务。\n", "\n", "--- 对话结束 ---\n"]}], "source": ["from llama_index.core.memory import ChatMemoryBuffer\n", "\n", "# 同样，创建一个新的内存实例\n", "memory = ChatMemoryBuffer.from_defaults(token_limit=2000)\n", "\n", "# 创建问题压缩聊天引擎\n", "condense_chat_engine = index.as_chat_engine(\n", "    chat_mode=\"condense_question\",\n", "    memory=memory,\n", "    verbose=True  # 开启详细模式，观察内部流程\n", ")\n", "\n", "# 开始第一轮对话\n", "print(\"--- 开始对话 (CondenseQuestionChatEngine) ---\\n\")\n", "user_message_1 = \"FlexiBot有什么功能？\"\n", "print(f\"👤 用户: {user_message_1}\")\n", "response_1 = condense_chat_engine.chat(user_message_1)\n", "print(f\"🤖 FlexiBot: {response_1}\\n\")\n", "\n", "# 第二轮对话，同样提出一个依赖上下文的问题\n", "# 注意观察 verbose=True 打印出的 'Condensing query:' 部分\n", "user_message_2 = \"详细介绍一下第二个功能。\"\n", "print(f\"👤 用户: {user_message_2}\")\n", "response_2 = condense_chat_engine.chat(user_message_2)\n", "print(f\"🤖 FlexiBot: {response_2}\\n\")\n", "\n", "print(\"--- 对话结束 ---\")"]}, {"cell_type": "markdown", "id": "markdown-summary", "metadata": {}, "source": ["## 5️⃣ 总结\n", "\n", "我们通过两个简单的例子，演示了 LlamaIndex 中处理对话历史的两种主要方式：\n", "\n", "- **`ContextChatEngine`**: 简单直接，将历史和知识都作为背景信息。适用于上下文依赖不强的场景。\n", "\n", "- **`CondenseQuestionChatEngine`**: 先重写问题再检索，逻辑更严谨，检索更准确。在需要精确理解用户意图、处理多轮模糊指代（如“它”、“那个”）的场景下，是更优的选择。\n", "\n", "在实际应用中，`condense_question` 模式通常更为健壮，能够提供更连贯和准确的对话体验。通过设置 `verbose=True`，我们可以清晰地看到 LlamaIndex 在背后为我们完成的复杂的提示工程和调用流程，这也是框架的强大之处。"]}, {"cell_type": "markdown", "id": "71a7e88f", "metadata": {}, "source": ["# LlamaIndex: system_prompt vs text_qa_template 区别\n", "\n", "## 核心差异\n", "\n", "| 特性 | system_prompt | text_qa_template |\n", "|------|---------------|------------------|\n", "| **作用范围** | 全局角色设定 | 具体任务模板 |\n", "| **内容类型** | 静态指令 | 动态模板（含变量） |\n", "| **使用场景** | 定义AI身份和行为 | 控制问答格式和逻辑 |\n", "| **设置方式** | 参数传入 | PromptTemplate对象 |\n", "\n", "## 详细对比\n", "\n", "### system_prompt - 系统角色提示\n", "\n", "**定义**：设定AI助手的身份、性格和基本行为规则\n", "\n", "```python\n", "# 简单设置\n", "chat_engine = index.as_chat_engine(\n", "    chat_mode=\"context\",\n", "    system_prompt=\"你是FlexiBot，一个友善的AI助手。请用专业但亲切的语气回答问题。\"\n", ")\n", "\n", "# 复杂设置\n", "system_prompt = \"\"\"你是一位资深的Python编程导师，名叫CodeMaster。\n", "\n", "你的特点：\n", "- 耐心细致，善于解释复杂概念\n", "- 总是提供可运行的代码示例\n", "- 会主动指出常见错误和最佳实践\n", "- 回答格式：概念解释 + 代码示例 + 注意事项\n", "\n", "请始终保持这种教学风格。\"\"\"\n", "```\n", "\n", "### text_qa_template - 问答任务模板\n", "\n", "**定义**：控制如何组织知识库内容、对话历史和用户问题的具体格式\n", "\n", "```python\n", "from llama_index.core import PromptTemplate\n", "\n", "# 自定义问答模板\n", "custom_qa_template = PromptTemplate(\n", "    \"\"\"基于以下信息回答问题：\n", "\n", "=== 相关知识 ===\n", "{context_str}\n", "\n", "=== 对话历史 ===\n", "{chat_history}\n", "\n", "=== 用户问题 ===\n", "{query_str}\n", "\n", "=== 回答要求 ===\n", "1. 必须基于上述知识回答\n", "2. 如果知识不足，明确说明\n", "3. 保持逻辑清晰\n", "\n", "回答：\"\"\"\n", ")\n", "\n", "# 应用模板\n", "chat_engine.update_prompts({\n", "    \"response_synthesizer:text_qa_template\": custom_qa_template\n", "})\n", "```\n", "\n", "## 实际作用机制\n", "\n", "### system_prompt 的作用位置\n", "```\n", "最终发送给LLM的内容：\n", "┌─────────────────────────────────┐\n", "│ System: 你是FlexiBot助手...      │  ← system_prompt\n", "├─────────────────────────────────┤\n", "│ User: 根据以下知识回答问题...    │  ← text_qa_template\n", "│ 知识库：...                     │\n", "│ 对话历史：...                   │\n", "│ 问题：...                       │\n", "└─────────────────────────────────┘\n", "```\n", "\n", "### text_qa_template 的变量替换\n", "```python\n", "# 模板中的变量会被自动替换：\n", "{context_str}   → 检索到的知识库内容\n", "{chat_history}  → 对话历史记录\n", "{query_str}     → 当前用户问题\n", "```\n", "\n", "## 使用场景对比\n", "\n", "### system_prompt 适用场景\n", "- ✅ **设定AI角色**：客服、导师、专家等\n", "- ✅ **定义回答风格**：正式、友善、幽默等\n", "- ✅ **设置行为规则**：安全限制、回答原则\n", "- ✅ **品牌化定制**：公司特色、产品调性\n", "\n", "```python\n", "# 客服机器人\n", "system_prompt = \"你是XX公司的客服代表小智，始终保持礼貌和专业。\"\n", "\n", "# 学习助手\n", "system_prompt = \"你是一位耐心的老师，善于用简单的例子解释复杂概念。\"\n", "```\n", "\n", "### text_qa_template 适用场景\n", "- ✅ **控制信息组织**：知识库、历史、问题的排列\n", "- ✅ **添加推理步骤**：思维链、分析过程\n", "- ✅ **格式化输出**：JSON、表格、列表等\n", "- ✅ **增加约束条件**：字数限制、引用要求\n", "\n", "```python\n", "# 结构化输出模板\n", "structured_template = PromptTemplate(\n", "    \"\"\"知识库：{context_str}\n", "问题：{query_str}\n", "\n", "请按以下格式回答：\n", "1. 核心答案：[一句话总结]\n", "2. 详细解释：[展开说明]\n", "3. 相关建议：[补充信息]\n", "\"\"\"\n", ")\n", "```\n", "\n", "## 组合使用示例\n", "\n", "```python\n", "# 完整的定制化聊天引擎\n", "from llama_index.core import PromptTemplate\n", "\n", "# 1. 设定AI身份和风格\n", "system_prompt = \"\"\"你是FlexiBot，一个专业的技术顾问。\n", "特点：准确、简洁、实用。每次回答都要体现专业性。\"\"\"\n", "\n", "# 2. 定制问答格式\n", "qa_template = PromptTemplate(\n", "    \"\"\"作为技术顾问，请基于以下信息回答：\n", "\n", "【技术文档】\n", "{context_str}\n", "\n", "【讨论历史】\n", "{chat_history}\n", "\n", "【技术问题】\n", "{query_str}\n", "\n", "【专业回答】\n", "请提供准确的技术解答，包含原理和实践建议：\"\"\"\n", ")\n", "\n", "# 3. 创建定制化引擎\n", "chat_engine = index.as_chat_engine(\n", "    chat_mode=\"context\",\n", "    system_prompt=system_prompt,  # 设定角色\n", "    memory=memory\n", ")\n", "\n", "# 4. 应用自定义模板\n", "chat_engine.update_prompts({\n", "    \"response_synthesizer:text_qa_template\": qa_template  # 控制格式\n", "})\n", "```\n", "\n", "## 最佳实践建议\n", "\n", "### system_prompt 设计原则\n", "- 🎯 **明确身份**：清楚定义AI的角色\n", "- 📏 **适度长度**：不要过长影响性能\n", "- 🔒 **安全边界**：设置必要的行为限制\n", "- 🎨 **风格一致**：保持品牌调性\n", "\n", "### text_qa_template 设计原则\n", "- 📋 **逻辑清晰**：信息组织有序\n", "- 🔧 **变量完整**：充分利用可用变量\n", "- 📝 **格式规范**：输出结构化\n", "- ⚡ **效率优先**：避免冗余内容\n", "\n", "## 调试技巧\n", "\n", "```python\n", "# 查看最终发送给LLM的完整内容\n", "class DebugChatEngine:\n", "    def chat(self, message):\n", "        # ... 检索和构建逻辑 ...\n", "        \n", "        print(\"=== System Prompt ===\")\n", "        print(system_prompt)\n", "        \n", "        print(\"=== Final Prompt ===\")\n", "        print(final_prompt)  # 包含 text_qa_template 处理后的内容\n", "        \n", "        return response\n", "```\n", "\n", "通过分别控制 `system_prompt`（角色设定）和 `text_qa_template`（任务格式），可以精确定制AI助手的行为和输出格式。\n", "````\n"]}, {"cell_type": "code", "execution_count": 18, "id": "630f3907", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== CondenseQuestionChatEngine 属性探索 ===\n", "可用属性: ['achat', 'astream_chat', 'callback_manager', 'chat', 'chat_history', 'chat_repl', 'from_defaults', 'reset', 'stream_chat', 'streaming_chat_repl']\n", "\n", "--- Query Engine 提示词 ---\n", "response_synthesizer:text_qa_template: Context information is below.\n", "---------------------\n", "{context_str}\n", "---------------------\n", "Given the context information and not prior knowledge, answer the query.\n", "Query: {query_str}\n", "Answer: \n", "response_synthesizer:refine_template: The original query is as follows: {query_str}\n", "We have provided an existing answer: {existing_answer}\n", "We have the opportunity to refine the existing answer (only if needed) with some more context below.\n", "------------\n", "{context_msg}\n", "------------\n", "Given the new context, refine the original answer to better answer the query. If the context isn't useful, return the original answer.\n", "Refined Answer: \n", "\n", "--- 问题压缩提示词 ---\n", "Given a conversation (between <PERSON> and Assistant) and a follow up message from <PERSON>, rewrite the message to be a standalone question that captures all relevant context from the conversation.\n", "\n", "<Chat History>\n", "{chat_history}\n", "\n", "<Follow Up Message>\n", "{question}\n", "\n", "<Standalone question>\n", "\n"]}], "source": ["# 方法1: 直接查看 CondenseQuestionChatEngine 的属性\n", "condense_chat_engine = index.as_chat_engine(\n", "    chat_mode=\"condense_question\",\n", "    memory=memory,\n", "    verbose=True\n", ")\n", "\n", "print(\"=== CondenseQuestionChatEngine 属性探索 ===\")\n", "print(\"可用属性:\", [attr for attr in dir(condense_chat_engine) if not attr.startswith('_')])\n", "\n", "# 尝试访问内部组件\n", "if hasattr(condense_chat_engine, '_query_engine'):\n", "    print(\"\\n--- Query Engine 提示词 ---\")\n", "    try:\n", "        query_prompts = condense_chat_engine._query_engine.get_prompts()\n", "        for key, prompt in query_prompts.items():\n", "            print(f\"{key}: {prompt.get_template()}\")\n", "    except:\n", "        print(\"无法获取 query_engine 提示词\")\n", "\n", "if hasattr(condense_chat_engine, '_condense_question_prompt'):\n", "    print(\"\\n--- 问题压缩提示词 ---\")\n", "    print(condense_chat_engine._condense_question_prompt.get_template())"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}