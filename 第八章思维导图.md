# 函数的核心概念与实践

## 函数基础

### 函数让重复任务只需写一次，提升代码复用性。

### 使用函数能使程序更易读、易测试、易修复。

### 函数通过调用来执行定义好的任务。

### 程序可通过调用函数多次执行相同操作。

### 函数定义通过 def 关键字和函数名实现。

### 函数可以通过模块分离，保持主程序整洁。

## 函数定义与调用

### def 关键字定义函数，函数名紧随其后，小括号内列出必要形参。

### 函数体由缩进代码块组成，可包含文档字符串描述功能。

### 调用函数时写函数名并加小括号，必要时传入实参。

### 无需参数的函数定义和调用括号不可省略。

### 函数体通常执行特定任务或显示信息。

## 参数类型

### 位置实参要求输入顺序与形参顺序一致，否则结果出错。

### 关键字实参形式为 name=value, 明确目标形参，无顺序要求。

### 默认值可为部分形参设默认，简化函数调用。

### 调用时可混用位置实参、关键字实参和默认参数。

### 必须为无默认值的参数最先列出并优先传递。

### 实参数量匹配很重要，出错时能获得友好的错误提示信息。

## 返回结果

### 返回值允许主程序以变量存储和使用函数输出结果。

### 返回值可以是简单数据、列表、字典等多种对象类型。

### 可用默认值与逻辑判断实现参数的可选性。

### 可构建复杂数据结构如字典来组织返回值。

### 返回值支持将数据处理逻辑与显示逻辑分离。

### 可选参数使函数调用更灵活适用多种场景。

## 参数传递与数据结构

### 函数接收列表参数后可遍历并操作列表各元素。

### 在函数内部直接修改传入列表会影响原列表。

### 若需保护原始数据可传递列表副本（list[:]）。

### 函数分工优化主程序结构，使每个函数各做一事。

### 适当组织函数使程序便于扩展和维护。

### 编写函数时应按需决定直接修改还是保护输入列表。

## 不定数量参数

### 使用\*args 收集不定数目的位置实参作为元组处理。

### 使用\*\*kwargs 收集不定数目的关键字实参为字典。

### 可结合普通参数与*args/*kwargs 灵活配置函数功能。

### 指定收集不定参数的形参须放在形参列表尾部。

### 常用形参名\*args、\*\*kwargs 约定可用于多数场合。

### 任意参数传递使函数写法更通用，适应复杂业务需求。

## 模块与导入

### import module 导入整个模块后用 module.function 调用函数。

### from module import func 可只导入需要的函数直接调用。

### as 关键字可以为模块或函数指定别名便于引用。

### from module import \* 可导入所有函数，但易引起命名冲突。

### 推荐导入需要的函数或整个模块而非全部，保持代码清晰。

### 将函数放模块中有利于多人协作与跨项目代码共享。

## 编码规范

### 函数与模块名称应简洁、全小写，使用下划线分隔。

### 每个函数应有简洁描述其功能的文档字符串。

### 给形参设默认值时等号两侧不加空格以符合 PEP8 规范。

### 代码行长度控制在 79 字符内，参数过多时可适度换行缩进。

### 多个函数应以空行分开，import 语句应集中放在文件开头。

### 良好命名和组织便于他人理解和复用你的代码。

## 使用优势

### 函数让重复的逻辑和处理只需实现一次。

### 修改函数即全局影响所有调用处，维护更省力。

### 良好命名和分工使程序逻辑一目了然。

### 函数有助于编写易于测试和调试的模块化代码。

### 对外隐藏内部实现细节，编程人员可专注于高层逻辑。

### 函数的合理使用为构建大型、可扩展程序打下基础。
