# -*- coding: utf-8 -*-
# ==============================================
# Demo: chat_mode="condense_plus_context"
# 目的：直观展示它的“三段式”流程：
#   1) 先把“历史+本轮用户话”压缩为【独立问题】
#   2) 用独立问题去【检索上下文】
#   3) 把【检索到的上下文 + 用户原话 + 系统提示词 + 历史信息】一起喂给模型生成答案
# 运行前：请把 OPENAI_API_KEY 设置成你自己的；如需自定义网关，设置 OPENAI_BASE_URL
# 无函数、无容错，尽量简单直白。
# ==============================================

import os
from llama_index.core import Settings, Document, VectorStoreIndex
import llama_index.core
from llama_index.llms.openai import OpenAI
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.vector_stores.qdrant import QdrantVectorStore
from qdrant_client import QdrantClient
from llama_index.core.memory import ChatMemoryBuffer

# —— 日志：打印每一次 LLM 的输入与输出（在 .py 中可见；在 Jupyter 里一般不可见）
llama_index.core.set_global_handler("simple")

# 替换成你自己的 OpenAI API Key
os.environ["OPENAI_API_KEY"] = "sk-qY0nr8zudg7Wc2bTR8EUV6rOTwfqZlU2ihwGL4pJ6m2ZEkEE"
# 如需自定义网关（如代理或 Azure），取消下面注释并替换
os.environ["OPENAI_BASE_URL"] = "https://api.openai-proxy.org/v1"



# ➊ 配置全局设置（替代 ServiceContext）
Settings.llm = OpenAI(
    model="gpt-4o-mini", 
    temperature=0.1,
    api_base="https://api.openai-proxy.org/v1"
)
Settings.embed_model = OpenAIEmbedding(
    model="text-embedding-3-small",
    api_base="https://api.openai-proxy.org/v1"
)

# ========== 1) 构造一个极小的“产品说明”知识库（三条文档） ==========
documents = [
    Document(
        text="FlexiBot 的第 1 个核心功能是多语言翻译：支持 50+ 语言的双向实时翻译。",
        metadata={"doc_name": "功能总览", "feature_id": "1_translation"}
    ),
    Document(
        text="FlexiBot 的第 2 个核心功能是代码生成：精通 Python / JavaScript / SQL，能把自然语言转成代码片段。",
        metadata={"doc_name": "功能总览", "feature_id": "2_coding"}
    ),
    Document(
        text="FlexiBot 的第 3 个核心功能是创意写作：适用于营销文案、诗歌、短篇故事等创作任务。",
        metadata={"doc_name": "功能总览", "feature_id": "3_writing"}
    ),
]

# —— 用内存版 Qdrant 存放向量；实际项目中请替换为持久化实例
client = QdrantClient(location=":memory:")
vector_store = QdrantVectorStore(client=client, collection_name="flexibot_docs")
index = VectorStoreIndex.from_documents(documents, vector_store=vector_store)

print("\n" + "="*72)
print("知识库已建立（Qdrant / 内存） ✅")
print("="*72 + "\n")

# ========== 2) 建立聊天引擎：condense_plus_context ==========
# 解释：相比 condense_question 只把“独立问题”喂给“查询引擎”再作答，
# condense_plus_context 会在最终回答阶段把“检索到的上下文 + 用户原话”都交给 LLM，
# 因此既能延续对话上下文，又能利用检索到的事实依据。
memory = ChatMemoryBuffer.from_defaults(token_limit=2000)
chat_engine = index.as_chat_engine(
    chat_mode="condense_plus_context",
    memory=memory,
    verbose=True,  # 打开 verbose 以便打印检索/提示词等中间过程
)

# ========== 3) 演示两轮对话：第 2 轮使用“指代” ==========\n
print("#"*72)
print("A. 第 1 轮：开启话题（让知识库进场）")
print("#"*72 + "\n")

user_1 = "FlexiBot 都有哪些核心功能？"
print("👤 用户：", user_1, "\n")
resp_1 = chat_engine.chat(user_1)

# —— 打印一个紧凑且易读的答案区块
print("—"*72)
print("🤖 答案（第 1 轮）")
print("—"*72)
print(str(resp_1).strip(), "\n")

# —— （可选）显示来源片段，帮助你确认检索是否命中
if getattr(resp_1, "source_nodes", None):
    print("📚 证据片段（命中 Top-K）")
    for i, sn in enumerate(resp_1.source_nodes, 1):
        preview = sn.node.get_content().strip().replace("\n", " ")
        if len(preview) > 120:
            preview = preview[:120] + "…"
        print(f"  [{i}] {preview}  | metadata={sn.node.metadata}")
    print()

print("#"*72)
print("B. 第 2 轮：带有“指代”的追问（需要先凝练再检索）")
print("#"*72 + "\n")

user_2 = "详细介绍一下第二个功能。"
print("👤 用户：", user_2, "\n")
resp_2 = chat_engine.chat(user_2)

print("—"*72)
print("🤖 答案（第 2 轮）")
print("—"*72)
print(str(resp_2).strip(), "\n")

if getattr(resp_2, "source_nodes", None):
    print("📚 证据片段（命中 Top-K）")
    for i, sn in enumerate(resp_2.source_nodes, 1):
        preview = sn.node.get_content().strip().replace("\n", " ")
        if len(preview) > 120:
            preview = preview[:120] + "…"
        print(f"  [{i}] {preview}  | metadata={sn.node.metadata}")
    print()

print("#"*72)

