{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# LlamaIndex PromptTemplate 速成课（费曼风格）\n", "> **一句话结论**：`PromptTemplate` 就是“**可填空的提示词模板**”。你先写一段带 **占位符**（`{...}`）的提示词，运行时把**真实内容**塞进去，得到**最终要发给大模型的字符串**。它的价值：**可重复、可控、可替换**。\n", "\n", "- 讲解风格：简洁直白 + 小例子 + 可复制代码 ✅  \n", "- 代码约定：**不自定义函数、不做容错**，能跑则跑；需要联网的单元请按需自行执行。  \n", "- 运行环境：Python 3.10+；需要安装 `llama-index`、`qdrant-client` 等。\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. PromptTemplate 是啥？（像“表格模板”🗂️）\n", "我们经常给大模型发这样的“指令 + 变量”组合：\n", "\n", "> 只能根据【上下文】回答问题；如果资料里没写，就说“未在资料中说明”。  \n", "> 上下文：……  \n", "> 问题：……\n", "\n", "把**变的部分**（上下文、问题）留成“空格”，等用的时候再**填空**。这张带空格的“表格”，在 LlamaIndex 里就叫 **PromptTemplate**。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.1 先用“纯字符串”理解模板（不依赖 LlamaIndex）"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 纯 Python 的“模板填空”示意（便于建立直觉）\n", "template = (\n", "    \"你是严谨的助教，只能依据上下文回答；不足就说'资料未说明'。\\\\n\"\n", "    \"上下文：{context_str}\\\\n\"\n", "    \"问题：{query_str}\\\\n\"\n", "    \"回答：\"\n", ")\n", "\n", "final_prompt = template.format(\n", "    context_str=\"《手册》第 2 章写了功能A和边界…\",\n", "    query_str=\"第二个功能到底支持哪些语言？\"\n", ")\n", "print(final_prompt)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 它解决了啥问题？（三个“省心”✅）\n", "1) **省重复**：相同结构的提示反复用，不用每次手敲一大段。  \n", "2) **省风格漂移**：统一口吻与约束（例如“必须引用证据/不可臆测”），降低幻觉风险。  \n", "3) **省耦合**：把“**说什么**”和“**怎么检索/怎么调用**”拆开。以后想换风格，只换模板，不改流程代码。\n", "\n", "> 实战常见：RAG 两阶段 **（先“凝练问题”→再“带上下文作答”）**，每一步各用一个模板，**精细控制**模型行为。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 在 LlamaIndex 里怎么用？（从“字符串”走到“引擎”🛠️）\n", "\n", "下面开始用最小可复现的代码展示“模板如何接到引擎”。\n", "> **注意**：以下单元若要真实调用模型，需要你自行设置 OpenAI 相关环境变量并联网；但即使不执行，也可作为“模板挂载样例”给团队学习。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.1 QA 阶段：给 `as_query_engine` 指定 `text_qa_template`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 安装依赖（如已安装可跳过）\n", "# %pip install -q llama-index qdrant-client\n", "\n", "from llama_index.core import VectorStoreIndex, Document, PromptTemplate\n", "\n", "# 简单语料与索引\n", "documents = [\n", "    Document(\"FlexiBot 的第二个功能是代码生成，支持 Python/JavaScript/SQL。\"),\n", "    Document(\"FlexiBot 的第一功能是多语言翻译，覆盖 50+ 语言。\"),\n", "]\n", "index = VectorStoreIndex.from_documents(documents)\n", "\n", "# 定义 QA 阶段模板：约束“只按上下文回答、不足就说未说明”\n", "qa_template = PromptTemplate(\n", "    \"仅依据上下文回答；若上下文不足，就说'资料未说明'：\\\\n\"\n", "    \"上下文：{context_str}\\\\n\"\n", "    \"问题：{query_str}\\\\n\"\n", "    \"回答：\"\n", ")\n", "\n", "# 把模板挂到 QueryEngine（不同版本参数名可能略有差异，核心思路一致）\n", "query_engine = index.as_query_engine(similarity_top_k=2, text_qa_template=qa_template)\n", "\n", "# 示例提问（需要联网和 API Key 才能真正调用模型）\n", "# print(query_engine.query('第二个功能是什么？'))"]}]}