# -*- coding: utf-8 -*-
# ==============================================
# Demo: chat_mode="condense_plus_context"（更详细的知识库版本）
# 目的：仅替换“知识库”为更充实的内容，验证“证据更厚”是否能减少幻觉。
# 约束：无函数、无容错，尽量简单；保留两轮对话与打印。
# ==============================================

import os
from llama_index.core import Settings, Document, VectorStoreIndex
import llama_index.core
from llama_index.llms.openai import OpenAI
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.vector_stores.qdrant import QdrantVectorStore
from qdrant_client import QdrantClient
from llama_index.core.memory import ChatMemoryBuffer

# —— 日志：打印每一次 LLM 的输入与输出（在 .py 中可见；在 Jupyter 里一般不可见）
llama_index.core.set_global_handler("simple")

# 替换成你自己的 OpenAI API Key
os.environ["OPENAI_API_KEY"] = "sk-qY0nr8zudg7Wc2bTR8EUV6rOTwfqZlU2ihwGL4pJ6m2ZEkEE"
# 如需自定义网关（如代理或 Azure），取消下面注释并替换
os.environ["OPENAI_BASE_URL"] = "https://api.openai-proxy.org/v1"



# ➊ 配置全局设置（替代 ServiceContext）
Settings.llm = OpenAI(
    model="gpt-4o-mini", 
    temperature=0.1,
    api_base="https://api.openai-proxy.org/v1"
)
Settings.embed_model = OpenAIEmbedding(
    model="text-embedding-3-small",
    api_base="https://api.openai-proxy.org/v1"
)

# ========== 1) 构造一个“更详细”的 FlexiBot 产品知识库（12 条文档） ==========
documents = [
    Document(
        text=(
            "【产品总览】FlexiBot 提供三大核心功能：\n"
            "1) 多语言翻译（feature_id=1_translation）：双向实时翻译，覆盖 50+ 语言；\n"
            "2) 代码生成（feature_id=2_coding）：将自然语言转为代码，主要覆盖 Python / JavaScript / SQL；\n"
            "3) 创意写作（feature_id=3_writing）：营销文案、诗歌、短篇等创作。\n"
            "本手册中的‘功能次序’以 feature_id 的序号为准：第一=翻译，第二=代码生成，第三=写作。\n"
        ),
        metadata={"doc_name": "产品总览", "feature_id": "0_overview", "section": "overview"}
    ),
    Document(
        text=(
            "【代码生成·能力定义】（feature_id=2_coding）\n"
            "主要能力：把用户的自然语言需求转为可运行或可阅读的代码片段。\n"
            "覆盖语言：Python、JavaScript、SQL（仅限这三种）。\n"
            "典型产出：函数、脚本片段、查询语句、简单单元测试。\n"
            "边界说明：仅生成代码，不负责云端执行、部署或调试环境搭建。\n"
        ),
        metadata={"doc_name": "代码生成-定义", "feature_id": "2_coding", "section": "definition"}
    ),
    Document(
        text=(
            "【代码生成·输入/输出格式】（feature_id=2_coding）\n"
            "输入建议：\n"
            " - 明确目标（例如：‘写一个计算列表平均值的 Python 函数’）。\n"
            " - 约束条件（例如：‘不使用第三方库’、‘时间复杂度尽量 O(n)’）。\n"
            "输出约定：\n"
            " - 以代码块形式展示；\n"
            " - 尽可能包含最小可运行示例；\n"
            " - 如有外部依赖，显式列出安装方式；\n"
            " - 若信息不足，明确标注假设或给出‘无法确定’提示。\n"
        ),
        metadata={"doc_name": "代码生成-IO", "feature_id": "2_coding", "section": "io"}
    ),
    Document(
        text=(
            "【代码生成·示例-Python】（feature_id=2_coding）\n"
            "需求：‘写一个计算两个数之和的 Python 函数，并附带一个最小示例。’\n"
            "参考产出：\n"
            "```python\n"
            "def add(a, b):\n"
            "    return a + b\n"
            "print(add(2, 3))  # 5\n"
            "```\n"
            "说明：示例尽量短小；若涉及边界条件（如类型校验），可在需求明确时再补充。\n"
        ),
        metadata={"doc_name": "代码生成-示例-Python", "feature_id": "2_coding", "section": "examples"}
    ),
    Document(
        text=(
            "【代码生成·示例-JavaScript】（feature_id=2_coding）\n"
            "需求：‘给一个求数组平均值的 JS 函数，并演示调用。’\n"
            "参考产出：\n"
            "```javascript\n"
            "function avg(arr){\n"
            "  if(!Array.isArray(arr) || arr.length===0) return null;\n"
            "  const s = arr.reduce((a,b)=>a+b, 0);\n"
            "  return s / arr.length;\n"
            "}\n"
            "console.log(avg([10,20,30])); // 20\n"
            "```\n"
        ),
        metadata={"doc_name": "代码生成-示例-JS", "feature_id": "2_coding", "section": "examples"}
    ),
    Document(
        text=(
            "【代码生成·示例-SQL】（feature_id=2_coding）\n"
            "需求：‘给出查询 2024 年订单总额的 SQL 示例（表 orders 有列 amount 与 order_date）。’\n"
            "参考产出：\n"
            "```sql\n"
            "SELECT SUM(amount) AS total_amount\n"
            "FROM orders\n"
            "WHERE order_date >= '2024-01-01' AND order_date < '2025-01-01';\n"
            "```\n"
        ),
        metadata={"doc_name": "代码生成-示例-SQL", "feature_id": "2_coding", "section": "examples"}
    ),
    Document(
        text=(
            "【代码生成·限制】（feature_id=2_coding）\n"
            "不支持的语言：C/C++/Java/Go 等。\n"
            "不提供的能力：\n"
            " - 远程执行代码；\n"
            " - 自动调试；\n"
            " - 大型工程脚手架的完整生成。\n"
            "安全边界：涉及敏感操作的代码会被拒绝或加上强提醒。\n"
        ),
        metadata={"doc_name": "代码生成-限制", "feature_id": "2_coding", "section": "limits"}
    ),
    Document(
        text=(
            "【代码生成·FAQ】（feature_id=2_coding）\n"
            "Q1：能否生成带第三方库的 Python 代码？可以，但需显式允许并列出可用库。\n"
            "Q2：能否给出性能保证？默认不给出严格复杂度保证。\n"
            "Q3：能否直接连接数据库生成 SQL？不直接连接，仅依据描述生成示例。\n"
        ),
        metadata={"doc_name": "代码生成-FAQ", "feature_id": "2_coding", "section": "faq"}
    ),
    Document(
        text=(
            "【多语言翻译·细节】（feature_id=1_translation）\n"
            "覆盖语言：50+，包含中/英/日/韩/法/德/西等。\n"
            "模式：实时对话翻译、文档段落翻译。\n"
            "限制：行业术语需用户提供术语表以稳定词汇。\n"
        ),
        metadata={"doc_name": "翻译-细节", "feature_id": "1_translation", "section": "details"}
    ),
    Document(
        text=(
            "【创意写作·细节】（feature_id=3_writing）\n"
            "适用场景：营销文案、社媒贴文、诗歌、短篇故事。\n"
            "风格控制：可指定语气与长度。\n"
        ),
        metadata={"doc_name": "写作-细节", "feature_id": "3_writing", "section": "details"}
    ),
    Document(
        text=(
            "【回答约束·证据优先】\n"
            "当用户要求‘详细介绍某功能’时，回答应：\n"
            " - 优先使用手册中的明确表述；\n"
            " - 对未出现的信息，回答‘未在资料中说明’；\n"
            " - 推断必须标注‘推断’并给理由。\n"
        ),
        metadata={"doc_name": "回答-约束", "feature_id": "policy", "section": "policy"}
    ),
]

# —— 用内存版 Qdrant 存放向量
client = QdrantClient(location=":memory:")
vector_store = QdrantVectorStore(client=client, collection_name="flexibot_docs_detailed")
index = VectorStoreIndex.from_documents(documents, vector_store=vector_store)

print("\n" + "="*72)
print("更详细的知识库已建立（Qdrant / 内存） ✅")
print("="*72 + "\n")

# ========== 2) 建立聊天引擎 ==========
memory = ChatMemoryBuffer.from_defaults(token_limit=2000)
chat_engine = index.as_chat_engine(
    chat_mode="condense_plus_context",
    memory=memory,
    verbose=True
)

# ========== 3) 演示两轮对话 ==========
print("#"*72)
print("A. 第 1 轮：开启话题（让知识库进场）")
print("#"*72 + "\n")

user_1 = "FlexiBot 都有哪些核心功能？"
print("👤 用户：", user_1, "\n")
resp_1 = chat_engine.chat(user_1)

print("—"*72)
print("🤖 答案（第 1 轮）")
print("—"*72)
print(str(resp_1).strip(), "\n")

if getattr(resp_1, "source_nodes", None):
    print("📚 证据片段（命中 Top-K）")
    for i, sn in enumerate(resp_1.source_nodes, 1):
        preview = sn.node.get_content().strip().replace("\n", " ")
        if len(preview) > 160:
            preview = preview[:160] + "…"
        print(f"  [{i}] {preview}  | metadata={sn.node.metadata}")
    print()

print("#"*72)
print("B. 第 2 轮：带有“指代”的追问（需要先凝练再检索）")
print("#"*72 + "\n")

user_2 = "详细介绍一下第二个功能。"
print("👤 用户：", user_2, "\n")
resp_2 = chat_engine.chat(user_2)

print("—"*72)
print("🤖 答案（第 2 轮）")
print("—"*72)
print(str(resp_2).strip(), "\n")

if getattr(resp_2, "source_nodes", None):
    print("📚 证据片段（命中 Top-K）")
    for i, sn in enumerate(resp_2.source_nodes, 1):
        preview = sn.node.get_content().strip().replace("\n", " ")
        if len(preview) > 160:
            preview = preview[:160] + "…"
        print(f"  [{i}] {preview}  | metadata={sn.node.metadata}")
    print()

print("#"*72)
print("🎯 观察要点：")
print("#"*72)
print("1) 第 2 轮的独立问题应更容易命中 feature_id=2_coding 的多条‘细化’文档；")
print("2) 回答若仍‘发挥’，可对照‘回答约束·证据优先’查看哪些信息不在资料中；")
print("3) 可改问法为‘只基于手册逐条总结第二个功能已明确写到的点，并引用关键词’。")
